package com.nucalale.app.repositories;

import com.nucalale.app.entities.AccountImages;
import com.nucalale.app.enums.AccountImagesType;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface AccountImagesRepository extends JpaRepository<AccountImages, String> {

    List<AccountImages> findByAccountIdAndType(String accountId, AccountImagesType type);
}
