package com.nucalale.app.services.impl;

import com.nucalale.app.dto.request.RequestCreateEditSchedule;
import com.nucalale.app.dto.request.RequestCreateShift;
import com.nucalale.app.dto.request.RequestCreateWorkUnit;
import com.nucalale.app.dto.response.ResponseDetailShift;
import com.nucalale.app.dto.response.ResponseDetailWorkUnit;
import com.nucalale.app.dto.response.ResponseListShift;
import com.nucalale.app.dto.response.ResponseListWorkUnit;
import com.nucalale.app.entities.*;
import com.nucalale.app.enums.ResponseEnum;
import com.nucalale.app.enums.TimeTableTypeEnum;
import com.nucalale.app.exceptions.NotFoundException;
import com.nucalale.app.repositories.*;
import com.nucalale.app.services.AccountService;
import com.nucalale.app.services.ScheduleService;
import com.nucalale.app.utils.EntityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class ScheduleServiceImpl implements ScheduleService {

    private final AgencyRepository agencyRepository;
    private final AccountService accountService;
    private final WorkUnitRepository workUnitRepository;
    private final TimeTableRepository timeTableRepository;
    private final LocationWorkUnitRadiusRepository locationWorkUnitRadiusRepository;
    private final ScheduleRepository scheduleRepository;
    private final AccountRepository accountRepository;
    private final ShiftRepository shiftRepository;

    @Override
    public ResponseEnum createWorkUnit(RequestCreateWorkUnit req) {
        log.info("Creating work unit with request: {}", req);
        
        try {
            String currentAccountId = accountService.getCurrentAccountId();
            log.debug("Current account ID: {}", currentAccountId);
            
            Agency agency = findAgencyById(req.getAgencyId());
            log.debug("Found agency: {}", agency.getName());
            
            WorkUnit workUnit = buildWorkUnit(req, agency, currentAccountId);
            workUnit = workUnitRepository.save(workUnit);
            log.info("Work unit created with ID: {}", workUnit.getId());
            
            createLocationWorkUnitRadius(req.getLocation(), workUnit, currentAccountId);
            log.debug("Created {} location radius", req.getLocation().size());
            
            if (req.getType() == TimeTableTypeEnum.REGULAR) {
                createWorkUnitRegular(req.getTimetable(), workUnit, currentAccountId);
                log.debug("Created regular timetable");
            } else {
                createWorkUnitShift(req.getShifts(), workUnit, currentAccountId, agency);
                log.debug("Created shift timetable");
            }
            
            log.info("Work unit creation completed successfully");
            return ResponseEnum.SUCCESS;
        } catch (Exception e) {
            log.error("Error creating work unit: ", e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ResponseListWorkUnit> getWorkUnitByAgencyId(String agencyId) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdDate");
        List<WorkUnit> workUnitList = workUnitRepository.findByAgencyId(agencyId, sort);
        
        return workUnitList.stream()
                .map(this::mapToResponseListWorkUnit)
                .toList();
    }

    @Override
    @Transactional(readOnly = true)
    public ResponseDetailWorkUnit getWorkUnitDetail(String workUnitId) {
        WorkUnit workUnit = findWorkUnitById(workUnitId);
        return mapToResponseDetailWorkUnit(workUnit);
    }

    @Override
    public ResponseEnum updateWorkUnit(String workUnitId, RequestCreateWorkUnit req) {
        Agency agency = findAgencyById(req.getAgencyId());
        WorkUnit workUnit = findWorkUnitById(workUnitId);
        updateWorkUnitFields(workUnit, req, agency);
        workUnitRepository.save(workUnit);
        
        return ResponseEnum.SUCCESS;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ResponseListWorkUnit> getWorkUnit(Pageable pageable, String agencyId, String query) {
        Page<WorkUnit> workUnitPage = workUnitRepository.findAndFilterWorkUnit(pageable, agencyId, query);
        List<ResponseListWorkUnit> responseList = workUnitPage.getContent().stream()
                .map(this::mapToResponseListWorkUnitBasic)
                .toList();
        
        return new PageImpl<>(responseList, pageable, workUnitPage.getTotalElements());
    }

    @Override
    public ResponseEnum createEditSchedule(RequestCreateEditSchedule req) {
        String currentAccountId = accountService.getCurrentAccountId();
        WorkUnit workUnit = findWorkUnitById(req.getWorkUnitId());
        Account account = findAccountById(req.getAccountId());
        Shift shift = findShiftById(req.getShiftId());

        Optional<Schedule> existingSchedule = scheduleRepository
                .findByDateAndAccountIdAndWorkUnitIdAndActiveIsTrue(
                        req.getDate(), req.getAccountId(), req.getWorkUnitId());

        Schedule schedule = existingSchedule.orElse(Schedule.builder()
                .date(req.getDate())
                .build());

        updateScheduleFields(schedule, account, workUnit, shift, currentAccountId, existingSchedule.isPresent());
        scheduleRepository.save(schedule);
        
        return ResponseEnum.SUCCESS;
    }

    @Override
    public ResponseEnum createShift(RequestCreateShift req) {
        String currentAccountId = accountService.getCurrentAccountId();
        Agency agency = findAgencyById(req.getAgencyId());
        WorkUnit workUnit = findWorkUnitById(req.getWorkUnitId());

        for (RequestCreateShift.RequestShiftTime shiftTime : req.getTimes()) {
            createShiftWithTimeTables(shiftTime, workUnit, agency, currentAccountId);
        }
        
        return ResponseEnum.SUCCESS;
    }

    @Override
    @Transactional(readOnly = true)
    public ResponseDetailShift getShift(String id) {
        Shift shift = findShiftById(id);
        return mapToResponseDetailShift(shift);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ResponseListShift> getShiftByWorkUnitId(String workUnitId) {
        List<Shift> shiftList = shiftRepository.findByWorkUnitIdAndActiveIsTrue(workUnitId);
        return shiftList.stream()
                .map(this::mapToResponseListShift)
                .toList();
    }

    private Agency findAgencyById(String agencyId) {
        return agencyRepository.findById(agencyId)
                .orElseThrow(() -> new NotFoundException(ResponseEnum.AGENCY_NOT_FOUND));
    }

    private WorkUnit findWorkUnitById(String workUnitId) {
        return workUnitRepository.findById(workUnitId)
                .orElseThrow(() -> new NotFoundException(ResponseEnum.WORK_UNIT_NOT_FOUND));
    }

    private Account findAccountById(String accountId) {
        return accountRepository.findById(accountId)
                .orElseThrow(() -> new NotFoundException(ResponseEnum.ACCOUNT_NOT_FOUND));
    }

    private Shift findShiftById(String shiftId) {
        return shiftRepository.findById(shiftId)
                .orElseThrow(() -> new NotFoundException(ResponseEnum.SHIT_NOT_FOUND));
    }

    private WorkUnit buildWorkUnit(RequestCreateWorkUnit req, Agency agency, String currentAccountId) {
        WorkUnit workUnit = WorkUnit.builder()
                .name(req.getName())
                .type(req.getType())
                .description(req.getDescription())
                .agency(agency)
                .build();
        EntityUtils.created(workUnit, currentAccountId);
        return workUnit;
    }

    private void createLocationWorkUnitRadius(List<RequestCreateWorkUnit.LocationRadius> locations, 
                                            WorkUnit workUnit, String currentAccountId) {
        List<LocationWorkUnitRadius> locationList = locations.stream()
                .map(location -> {
                    LocationWorkUnitRadius radius = LocationWorkUnitRadius.builder()
                            .name(location.getName())
                            .latitude(location.getLat())
                            .longitude(location.getLng())
                            .radius(location.getRadius())
                            .workUnit(workUnit)
                            .build();
                    EntityUtils.created(radius, currentAccountId);
                    return radius;
                })
                .toList();
        
        locationWorkUnitRadiusRepository.saveAll(locationList);
    }

    private void updateWorkUnitFields(WorkUnit workUnit, RequestCreateWorkUnit req, Agency agency) {
        workUnit.setName(req.getName());
        workUnit.setDescription(req.getDescription());
        workUnit.setAgency(agency);
        EntityUtils.updated(workUnit, accountService.getCurrentAccountId());
    }

    private void updateScheduleFields(Schedule schedule, Account account, WorkUnit workUnit, 
                                    Shift shift, String currentAccountId, boolean isUpdate) {
        schedule.setAccount(account);
        schedule.setWorkUnit(workUnit);
        schedule.setShift(shift);
        
        if (isUpdate) {
            EntityUtils.updated(schedule, currentAccountId);
        } else {
            EntityUtils.created(schedule, currentAccountId);
        }
    }

    private void createShiftWithTimeTables(RequestCreateShift.RequestShiftTime shiftTime, 
                                         WorkUnit workUnit, Agency agency, String currentAccountId) {
        Shift shift = Shift.builder()
                .name(shiftTime.getName())
                .startTime(shiftTime.getStartTime())
                .endTime(shiftTime.getEndTime())
                .workUnit(workUnit)
                .agency(agency)
                .build();
        EntityUtils.created(shift, currentAccountId);
        shift = shiftRepository.save(shift);

        createTimeTablesForShift(shiftTime.getTimeTables(), workUnit, shift, currentAccountId);
    }

    private void createTimeTablesForShift(List<RequestCreateShift.ShiftTimeTable> timeTables, 
                                        WorkUnit workUnit, Shift shift, String currentAccountId) {
        List<TimeTable> timeTableList = new ArrayList<>();
        
        for (int i = 0; i < timeTables.size(); i++) {
            RequestCreateShift.ShiftTimeTable tableData = timeTables.get(i);
            TimeTable timeTable = TimeTable.builder()
                    .startTime(tableData.getStartTime())
                    .endTime(tableData.getEndTime())
                    .name(tableData.getName())
                    .workUnit(workUnit)
                    .shift(shift)
                    .type(TimeTableTypeEnum.SHIFT)
                    .day(i)
                    .build();
            EntityUtils.created(timeTable, currentAccountId);
            timeTableList.add(timeTable);
        }
        
        timeTableRepository.saveAll(timeTableList);
    }

    private ResponseListWorkUnit mapToResponseListWorkUnit(WorkUnit workUnit) {
        return ResponseListWorkUnit.builder()
                .id(workUnit.getId())
                .type(workUnit.getType())
                .agencyId(workUnit.getAgency().getId())
                .agencyName(workUnit.getAgency().getName())
                .name(workUnit.getName())
                .totalDay(timeTableRepository.getTotalDayByWorkUnitId(workUnit.getId()))
                .countLocation(locationWorkUnitRadiusRepository.countLocationWorkUnitRadiusByWorkUnitId(workUnit.getId()))
                .description(workUnit.getDescription())
                .build();
    }

    private ResponseListWorkUnit mapToResponseListWorkUnitBasic(WorkUnit workUnit) {
        return ResponseListWorkUnit.builder()
                .type(workUnit.getType())
                .id(workUnit.getId())
                .agencyId(workUnit.getAgency().getId())
                .agencyName(workUnit.getAgency().getName())
                .name(workUnit.getName())
                .description(workUnit.getDescription())
                .build();
    }

    private ResponseDetailWorkUnit mapToResponseDetailWorkUnit(WorkUnit workUnit) {
        return ResponseDetailWorkUnit.builder()
                .id(workUnit.getId())
                .agencyId(workUnit.getAgency().getId())
                .type(workUnit.getType())
                .agencyName(workUnit.getAgency().getName())
                .name(workUnit.getName())
                .description(workUnit.getDescription())
                .build();
    }

    private ResponseDetailShift mapToResponseDetailShift(Shift shift) {
        return ResponseDetailShift.builder()
                .id(shift.getId())
                .name(shift.getName())
                .description(shift.getDescription())
                .agencyId(shift.getAgency().getId())
                .agencyName(shift.getAgency().getName())
                .workUnitId(shift.getWorkUnit().getId())
                .startTime(shift.getStartTime())
                .endTime(shift.getEndTime())
                .workUnitName(shift.getWorkUnit().getName())
                .timeTables(getTimeTablesDetailShift(shift.getId()))
                .build();
    }

    private ResponseListShift mapToResponseListShift(Shift shift) {
        return ResponseListShift.builder()
                .id(shift.getId())
                .name(shift.getName())
                .description(shift.getDescription())
                .agencyId(shift.getAgency().getId())
                .agencyName(shift.getAgency().getName())
                .workUnitId(shift.getWorkUnit().getId())
                .startTime(shift.getStartTime())
                .endTime(shift.getEndTime())
                .workUnitName(shift.getWorkUnit().getName())
                .build();
    }

    private List<ResponseDetailShift.ShiftTimeTable> getTimeTablesDetailShift(String shiftId) {
        List<TimeTable> timeTables = timeTableRepository.findByShiftIdAndTypeAndActiveIsTrue(shiftId, TimeTableTypeEnum.SHIFT);
        return timeTables.stream()
                .map(timeTable -> ResponseDetailShift.ShiftTimeTable.builder()
                        .name(timeTable.getName())
                        .startTime(timeTable.getStartTime())
                        .endTime(timeTable.getEndTime())
                        .build())
                .toList();
    }

    private void createWorkUnitRegular(List<RequestCreateWorkUnit.TimetableData> timetableDataList, 
                                     WorkUnit workUnit, String currentAccountId) {
        List<TimeTable> timeTableList = timetableDataList.stream()
                .map(timetableData -> {
                    TimeTable timeTable = TimeTable.builder()
                            .day(timetableData.getDay())
                            .startTime(timetableData.getStartTime())
                            .endTime(timetableData.getEndTime())
                            .name(timetableData.getName())
                            .description(timetableData.getDescription())
                            .workUnit(workUnit)
                            .build();
                    EntityUtils.created(timeTable, currentAccountId);
                    return timeTable;
                })
                .toList();
        
        timeTableRepository.saveAll(timeTableList);
    }

    private void createWorkUnitShift(List<RequestCreateWorkUnit.ShiftData> shifts, 
                                   WorkUnit workUnit, String currentAccountId, Agency agency) {
        for (RequestCreateWorkUnit.ShiftData shiftData : shifts) {
            Shift shift = Shift.builder()
                    .name(shiftData.getName())
                    .startTime(shiftData.getStartTime())
                    .endTime(shiftData.getEndTime())
                    .workUnit(workUnit)
                    .agency(agency)
                    .build();
            EntityUtils.created(shift, currentAccountId);
            shift = shiftRepository.save(shift);

            createTimeTablesForWorkUnitShift(shiftData.getTimeTables(), workUnit, shift, currentAccountId);
        }
    }

    private void createTimeTablesForWorkUnitShift(List<RequestCreateWorkUnit.ShiftTimeTable> timeTables, 
                                                WorkUnit workUnit, Shift shift, String currentAccountId) {
        List<TimeTable> timeTableList = new ArrayList<>();
        
        for (int i = 0; i < timeTables.size(); i++) {
            RequestCreateWorkUnit.ShiftTimeTable tableData = timeTables.get(i);
            TimeTable timeTable = TimeTable.builder()
                    .startTime(tableData.getStartTime())
                    .endTime(tableData.getEndTime())
                    .name(tableData.getName())
                    .workUnit(workUnit)
                    .shift(shift)
                    .type(TimeTableTypeEnum.SHIFT)
                    .day(i)
                    .build();
            EntityUtils.created(timeTable, currentAccountId);
            timeTableList.add(timeTable);
        }
        
        timeTableRepository.saveAll(timeTableList);
    }
}