<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="create-account-table" author="rivo">
        <createTable tableName="account">
            <column name="id" type="VARCHAR(32)">
                <constraints primaryKey="true" nullable="false"/>
            </column>

            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>

            <column name="phone" type="VARCHAR(50)"/>
            <column name="role" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>

            <column name="profile_picture" type="VARCHAR(255)"/>
            <column name="email" type="VARCHAR(255)"/>
            <column name="password" type="TEXT">
                <constraints nullable="false"/>
            </column>

            <column name="nip" type="VARCHAR(50)"/>
            <column name="nik" type="VARCHAR(50)"/>
            <column name="government_rank" type="VARCHAR(100)"/>
            <column name="front_degree" type="VARCHAR(50)"/>
            <column name="back_degree" type="VARCHAR(50)"/>
            <column name="place_or_birth" type="bigint"/>
            <column name="religion" type="VARCHAR(50)"/>
            <column name="gender" type="VARCHAR(20)"/>
            <column name="marital_status" type="VARCHAR(20)"/>
            <column name="date_of_birth" type="DATE"/>
            <column name="tax_id_number" type="VARCHAR(50)"/>
            <column name="address" type="TEXT"/>
            <column name="sub_district_id" type="bigint"/>

            <!-- base entity fields -->
            <column name="active" type="BOOLEAN"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="created_date" type="TIMESTAMP"/>
            <column name="deleted_by" type="VARCHAR(255)"/>
            <column name="deleted_date" type="TIMESTAMP"/>
            <column name="updated_by" type="VARCHAR(255)"/>
            <column name="updated_date" type="TIMESTAMP"/>
        </createTable>

        <!-- foreign keys -->
        <addForeignKeyConstraint
                constraintName="fk_account_sub_district"
                baseTableName="account"
                baseColumnNames="sub_district_id"
                referencedTableName="sub_district"
                referencedColumnNames="id"
                onDelete="SET NULL"/>

        <addForeignKeyConstraint
                constraintName="fk_account_place_or_birth"
                baseTableName="account"
                baseColumnNames="place_or_birth"
                referencedTableName="sub_district"
                referencedColumnNames="id"
                onDelete="SET NULL"/>

    </changeSet>
</databaseChangeLog>