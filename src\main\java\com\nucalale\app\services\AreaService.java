package com.nucalale.app.services;

import com.nucalale.app.dto.response.ResponseAreaData;

import java.math.BigInteger;
import java.util.List;

public interface AreaService {
    List<ResponseAreaData> listProvince();

    List<ResponseAreaData> listCity(BigInteger id);

    List<ResponseAreaData> listDistrict(BigInteger id);

    List<ResponseAreaData> listSubDistrict(BigInteger id);

    List<ResponseAreaData> listCityBounce(String query);
}
