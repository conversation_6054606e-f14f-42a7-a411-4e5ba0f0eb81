spring.application.name=<PERSON><PERSON>-<PERSON>e
#REDIST
spring.data.redis.host=*************
spring.data.redis.port=6379
spring.cache.type=redis
# DATABASE
spring.datasource.url=*********************************************************
spring.datasource.username=rivo
spring.datasource.password=@Rivopelu123
#Liquibase
spring.liquibase.enabled=false
spring.liquibase.change-log=classpath:/db/changelog/changelog-master.xml
# app
spring.jpa.hibernate.ddl-auto=none
server.servlet.context-path=/api
spring.data.web.pageable.default-page-size=10
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
spring.jpa.show-sql=false
# AUTH
auth.secret=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
auth.expiration_time=********

# WHATSAPP FONNTE
whatsapp.fonnte.url=https://api.fonnte.com/send
whatsapp.fonnte.token=WnBPWrSWcZzqPJTkorZ2
server.port=9765

auth.first.account.name=admin
auth.first.account.email=<EMAIL>
auth.first.account.password=admin

url.face-recognition.service=http://*************:8000/api

# ========================================
# OPENTELEMETRY CONFIGURATION
# ========================================

# Service Information
otel.service.name=nuca-lale-backend
otel.service.version=0.0.1-SNAPSHOT
otel.service.namespace=com.nucalale.app

# Resource Attributes - Additional metadata about your service
otel.resource.attributes=service.name=nuca-lale-backend,service.version=0.0.1-SNAPSHOT,deployment.environment=development,service.instance.id=${HOSTNAME:localhost}

# Instrumentation Configuration
otel.instrumentation.common.default-enabled=true

# HTTP Instrumentation
otel.instrumentation.http.client.enabled=true
otel.instrumentation.http.server.enabled=true
otel.instrumentation.spring-web.enabled=true
otel.instrumentation.spring-webmvc.enabled=true

# Database Instrumentation
otel.instrumentation.jdbc.enabled=true
otel.instrumentation.hibernate.enabled=true
otel.instrumentation.jpa.enabled=true

# Redis Instrumentation
otel.instrumentation.lettuce.enabled=true
otel.instrumentation.jedis.enabled=true

# Security and JWT Instrumentation
otel.instrumentation.spring-security.enabled=true

# JVM and Runtime Metrics
otel.instrumentation.runtime-telemetry.enabled=true
otel.instrumentation.jvm.enabled=true

# Sampling Configuration
# For development: sample all traces (1.0 = 100%)
# For production: consider lower values like 0.1 (10%) for performance
otel.traces.sampler=traceidratio
otel.traces.sampler.arg=1.0

# Batch Span Processor Configuration
otel.bsp.schedule.delay=500ms
otel.bsp.max.queue.size=2048
otel.bsp.max.export.batch.size=512
otel.bsp.export.timeout=30s

# ========================================
# EXPORTER CONFIGURATION
# ========================================

# Primary: Console Exporter (for development and testing)
otel.traces.exporter=console
otel.metrics.exporter=console
otel.logs.exporter=console

# Console Exporter Configuration
otel.exporter.console.traces.enabled=true
otel.exporter.console.metrics.enabled=true

# OTLP Exporter Configuration (commented out - ready to enable)
# Uncomment and configure these for production use with OTLP-compatible backends
#otel.traces.exporter=otlp
#otel.metrics.exporter=otlp
#otel.logs.exporter=otlp
#otel.exporter.otlp.endpoint=http://localhost:4317
#otel.exporter.otlp.protocol=grpc
#otel.exporter.otlp.timeout=10s
#otel.exporter.otlp.compression=gzip
#otel.exporter.otlp.headers=api-key=your-api-key

# Jaeger Exporter Configuration (alternative - commented out)
#otel.traces.exporter=jaeger
#otel.exporter.jaeger.endpoint=http://localhost:14250
#otel.exporter.jaeger.timeout=10s

# Zipkin Exporter Configuration (alternative - commented out)
#otel.traces.exporter=zipkin
#otel.exporter.zipkin.endpoint=http://localhost:9411/api/v2/spans
#otel.exporter.zipkin.timeout=10s

# Prometheus Metrics Exporter Configuration (alternative - commented out)
#otel.metrics.exporter=prometheus
#otel.exporter.prometheus.port=9464
#otel.exporter.prometheus.host=localhost

# ========================================
# LOGGING CORRELATION CONFIGURATION
# ========================================

# Enable trace context in logs
logging.pattern.level=%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]

# ========================================
# PERFORMANCE AND DEBUGGING
# ========================================

# Enable/disable OpenTelemetry SDK (useful for debugging)
otel.sdk.disabled=false

# Suppress specific instrumentation if needed (uncomment as needed)
#otel.instrumentation.spring-boot-autoconfigure.enabled=false
#otel.instrumentation.logback-appender.enabled=false

# Debug logging for OpenTelemetry (enable only for troubleshooting)
#otel.javaagent.debug=true
#logging.level.io.opentelemetry=DEBUG