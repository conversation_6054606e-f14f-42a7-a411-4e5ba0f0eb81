package com.nucalale.app.repositories;

import com.nucalale.app.entities.City;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.math.BigInteger;
import java.util.List;

public interface CityRepository extends JpaRepository<City, BigInteger> {
    List<City> findByProvinceId(BigInteger province_id);

    @Query("SELECT c FROM City c WHERE LOWER(c.name) LIKE LOWER(CONCAT('%', :query, '%')) ORDER BY c.name ASC")
    List<City> findNameBounce(String query);
}

