<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="1.2.0" author="rivopelu">
        <createTable tableName="otp_and_token">
            <column name="id" type="VARCHAR(32)">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="data" type="varchar(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="type" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="account_id" type="varchar(36)"/>
        </createTable>
        <addForeignKeyConstraint baseTableName="otp_and_token" baseColumnNames="account_id" constraintName="account_token_and_otp"
                                 referencedTableName="account"
                                 referencedColumnNames="id"/>
    </changeSet>
</databaseChangeLog>