package com.nucalale.app.observability;

import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.api.metrics.LongHistogram;
import io.opentelemetry.api.metrics.Meter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * Custom Metrics for Nuca-Lale Application
 * 
 * This class defines and manages custom business metrics for observability.
 * These metrics complement the automatic metrics provided by OpenTelemetry auto-instrumentation.
 */
@Component
@Slf4j
public class CustomMetrics {

    @Autowired
    private Meter meter;

    // Authentication Metrics
    private LongCounter loginAttemptsCounter;
    private LongCounter loginSuccessCounter;
    private LongCounter loginFailureCounter;
    private LongHistogram authenticationDuration;

    // Employee Management Metrics
    private LongCounter employeeRegistrationCounter;
    private LongCounter employeeUpdateCounter;
    private LongHistogram employeeOperationDuration;

    // Schedule Management Metrics
    private LongCounter scheduleCreationCounter;
    private LongCounter workUnitCreationCounter;
    private LongHistogram scheduleOperationDuration;

    // Face Recognition Metrics
    private LongCounter faceRecognitionRequestCounter;
    private LongHistogram faceRecognitionDuration;

    // OTP Metrics
    private LongCounter otpGenerationCounter;
    private LongCounter otpValidationCounter;
    private LongCounter otpValidationSuccessCounter;

    @PostConstruct
    public void initializeMetrics() {
        log.info("Initializing custom metrics for Nuca-Lale application");

        // Authentication Metrics
        loginAttemptsCounter = meter.counterBuilder("nuca_lale_login_attempts_total")
                .setDescription("Total number of login attempts")
                .setUnit("1")
                .build();

        loginSuccessCounter = meter.counterBuilder("nuca_lale_login_success_total")
                .setDescription("Total number of successful logins")
                .setUnit("1")
                .build();

        loginFailureCounter = meter.counterBuilder("nuca_lale_login_failure_total")
                .setDescription("Total number of failed logins")
                .setUnit("1")
                .build();

        authenticationDuration = meter.histogramBuilder("nuca_lale_authentication_duration_ms")
                .setDescription("Duration of authentication operations")
                .setUnit("ms")
                .ofLongs()
                .build();

        // Employee Management Metrics
        employeeRegistrationCounter = meter.counterBuilder("nuca_lale_employee_registration_total")
                .setDescription("Total number of employee registrations")
                .setUnit("1")
                .build();

        employeeUpdateCounter = meter.counterBuilder("nuca_lale_employee_update_total")
                .setDescription("Total number of employee updates")
                .setUnit("1")
                .build();

        employeeOperationDuration = meter.histogramBuilder("nuca_lale_employee_operation_duration_ms")
                .setDescription("Duration of employee operations")
                .setUnit("ms")
                .ofLongs()
                .build();

        // Schedule Management Metrics
        scheduleCreationCounter = meter.counterBuilder("nuca_lale_schedule_creation_total")
                .setDescription("Total number of schedules created")
                .setUnit("1")
                .build();

        workUnitCreationCounter = meter.counterBuilder("nuca_lale_work_unit_creation_total")
                .setDescription("Total number of work units created")
                .setUnit("1")
                .build();

        scheduleOperationDuration = meter.histogramBuilder("nuca_lale_schedule_operation_duration_ms")
                .setDescription("Duration of schedule operations")
                .setUnit("ms")
                .ofLongs()
                .build();

        // Face Recognition Metrics
        faceRecognitionRequestCounter = meter.counterBuilder("nuca_lale_face_recognition_requests_total")
                .setDescription("Total number of face recognition requests")
                .setUnit("1")
                .build();

        faceRecognitionDuration = meter.histogramBuilder("nuca_lale_face_recognition_duration_ms")
                .setDescription("Duration of face recognition operations")
                .setUnit("ms")
                .ofLongs()
                .build();

        // OTP Metrics
        otpGenerationCounter = meter.counterBuilder("nuca_lale_otp_generation_total")
                .setDescription("Total number of OTP generations")
                .setUnit("1")
                .build();

        otpValidationCounter = meter.counterBuilder("nuca_lale_otp_validation_total")
                .setDescription("Total number of OTP validations")
                .setUnit("1")
                .build();

        otpValidationSuccessCounter = meter.counterBuilder("nuca_lale_otp_validation_success_total")
                .setDescription("Total number of successful OTP validations")
                .setUnit("1")
                .build();

        log.info("Custom metrics initialized successfully");
    }

    // Authentication Metrics Methods
    public void recordLoginAttempt(String userType, String method) {
        loginAttemptsCounter.add(1, Attributes.of(
                AttributeKey.stringKey("user.type"), userType,
                AttributeKey.stringKey("auth.method"), method
        ));
    }

    public void recordLoginSuccess(String userType, String method) {
        loginSuccessCounter.add(1, Attributes.of(
                AttributeKey.stringKey("user.type"), userType,
                AttributeKey.stringKey("auth.method"), method
        ));
    }

    public void recordLoginFailure(String userType, String method, String reason) {
        loginFailureCounter.add(1, Attributes.of(
                AttributeKey.stringKey("user.type"), userType,
                AttributeKey.stringKey("auth.method"), method,
                AttributeKey.stringKey("failure.reason"), reason
        ));
    }

    public void recordAuthenticationDuration(long durationMs, String operation) {
        authenticationDuration.record(durationMs, Attributes.of(
                AttributeKey.stringKey("auth.operation"), operation
        ));
    }

    // Employee Management Metrics Methods
    public void recordEmployeeRegistration(String status) {
        employeeRegistrationCounter.add(1, Attributes.of(
                AttributeKey.stringKey("registration.status"), status
        ));
    }

    public void recordEmployeeUpdate(String operation) {
        employeeUpdateCounter.add(1, Attributes.of(
                AttributeKey.stringKey("update.operation"), operation
        ));
    }

    public void recordEmployeeOperationDuration(long durationMs, String operation) {
        employeeOperationDuration.record(durationMs, Attributes.of(
                AttributeKey.stringKey("employee.operation"), operation
        ));
    }

    // Schedule Management Metrics Methods
    public void recordScheduleCreation(String type) {
        scheduleCreationCounter.add(1, Attributes.of(
                AttributeKey.stringKey("schedule.type"), type
        ));
    }

    public void recordWorkUnitCreation(String agencyId) {
        workUnitCreationCounter.add(1, Attributes.of(
                AttributeKey.stringKey("agency.id"), agencyId
        ));
    }

    public void recordScheduleOperationDuration(long durationMs, String operation) {
        scheduleOperationDuration.record(durationMs, Attributes.of(
                AttributeKey.stringKey("schedule.operation"), operation
        ));
    }

    // Face Recognition Metrics Methods
    public void recordFaceRecognitionRequest(String status) {
        faceRecognitionRequestCounter.add(1, Attributes.of(
                AttributeKey.stringKey("recognition.status"), status
        ));
    }

    public void recordFaceRecognitionDuration(long durationMs) {
        faceRecognitionDuration.record(durationMs);
    }

    // OTP Metrics Methods
    public void recordOtpGeneration(String type) {
        otpGenerationCounter.add(1, Attributes.of(
                AttributeKey.stringKey("otp.type"), type
        ));
    }

    public void recordOtpValidation(String result) {
        otpValidationCounter.add(1, Attributes.of(
                AttributeKey.stringKey("validation.result"), result
        ));
    }

    public void recordOtpValidationSuccess() {
        otpValidationSuccessCounter.add(1);
    }
}
