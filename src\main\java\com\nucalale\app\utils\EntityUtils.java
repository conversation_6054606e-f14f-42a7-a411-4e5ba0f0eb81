package com.nucalale.app.utils;


import com.nucalale.app.entities.BaseEntity;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

import static com.nucalale.app.utils.UtilsHelper.getUUID;

public class EntityUtils {

    public static <T extends BaseEntity> void created(T entity, String userId) {
        if (entity.getId() == null || entity.getId().isBlank())
            entity.setId(getUUID());
        if (entity.getCreatedBy() == null || entity.getCreatedBy().isBlank())
            entity.setCreatedBy(userId);
        if (entity.getCreatedDate() == null)
            entity.setCreatedDate(LocalDateTime.now());
        if (entity.getUpdatedDate() == null)
            entity.setCreatedDate(LocalDateTime.now());
        if (entity.getUpdatedDate() == null || entity.getUpdatedBy().isBlank())
            entity.setUpdatedBy(userId);
        if (entity.getActive() == null)
            entity.setActive(true);
    }


    public static <T extends BaseEntity> void updated(T entity, String userId) {
        entity.setUpdatedBy(userId);
        entity.setUpdatedDate(LocalDateTime.now());
    }
}




