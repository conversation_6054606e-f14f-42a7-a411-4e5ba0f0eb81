<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="1.0.4" author="rivo pelu">
        <createTable tableName="reference_old_data">
            <column name="id" type="varchar(255)">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="varchar(256)"/>
            <column name="nip" type="varchar(64)"/>
            <column name="old_nip" type="varchar(64)"/>
            <column name="nik" type="varchar(64)"/>
            <column name="government_rank" type="varchar(128)"/>
            <column name="front_degree" type="varchar(128)"/>
            <column name="back_degree" type="varchar(128)"/>
            <column name="religion" type="varchar(64)"/>
            <column name="date_of_birth" type="date"/>
            <column name="tax_id_number" type="varchar(64)"/>
            <column name="marital_status" type="varchar(64)"/>
            <column name="gender" type="varchar(64)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>