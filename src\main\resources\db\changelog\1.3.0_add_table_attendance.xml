<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="1.3.0" author="rivo pelu">
        <createTable tableName="attendance">
            <column name="id" type="VARCHAR(32)">
                <constraints primaryKey="true" nullable="false"/>
            </column>

            <column name="work_unit_id" type="varchar(40)"/>
            <column name="time_table_id" type="varchar(40)"/>
            <column name="account_id" type="varchar(40)"/>
            <column name="location_work_unit_radius_id" type="varchar(40)"/>
            <column name="lat" type="double"/>
            <column name="lng" type="double"/>

            <!-- base entity fields -->
            <column name="active" type="BOOLEAN"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="created_date" type="TIMESTAMP"/>
            <column name="deleted_by" type="VARCHAR(255)"/>
            <column name="deleted_date" type="TIMESTAMP"/>
            <column name="updated_by" type="VARCHAR(255)"/>
            <column name="updated_date" type="TIMESTAMP"/>
        </createTable>

        <addForeignKeyConstraint
                baseTableName="attendance"
                baseColumnNames="work_unit_id"
                constraintName="work_unit_attendance_fk"
                referencedTableName="work_unit"
                referencedColumnNames="id"
        />

        <addForeignKeyConstraint
                baseTableName="attendance"
                baseColumnNames="time_table_id"
                constraintName="time_table_attendance_fk"
                referencedTableName="time_table"
                referencedColumnNames="id"
        />
        <addForeignKeyConstraint
                baseTableName="attendance"
                baseColumnNames="account_id"
                constraintName="account_attendance_fk"
                referencedTableName="account"
                referencedColumnNames="id"
        />
        <addForeignKeyConstraint
                baseTableName="attendance"
                baseColumnNames="location_work_unit_radius_id"
                constraintName="location_work_unit_radius_attendance_fk"
                referencedTableName="location_work_unit_radius"
                referencedColumnNames="id"
        />
    </changeSet>
</databaseChangeLog>