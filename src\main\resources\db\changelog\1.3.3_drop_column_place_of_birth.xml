<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="1.3.3" author="rivo pelu">
        <dropForeignKeyConstraint baseTableName="account" constraintName="fk_account_place_or_birth"/>
        <dropColumn tableName="account" columnName="place_or_birth"/>

        <addColumn tableName="account">
            <column name="place_of_birth_city_id" type="integer"/>
        </addColumn>

        <addForeignKeyConstraint
                baseTableName="account"
                baseColumnNames="place_of_birth_city_id"
                constraintName="place_of_birth_city_fk"
                referencedTableName="city"
                referencedColumnNames="id"
        />
    </changeSet>
</databaseChangeLog>