package com.nucalale.app.configs;


import com.nucalale.app.entities.Account;
import com.nucalale.app.enums.AccountRoleEnum;
import com.nucalale.app.repositories.AccountRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class PasswordAuthenticationProvider implements AuthenticationProvider {

    private final AccountRepository accountRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String username = authentication.getName();
        String password = authentication.getCredentials().toString();

        Account account = findAccountByIdentifier(username);

        if (account == null) {
            throw new BadCredentialsException("Invalid username or password");
        }

        if (!account.getActive()) {
            throw new DisabledException("Account is disabled");
        }

        if (!passwordEncoder.matches(password, account.getPassword())) {
            throw new BadCredentialsException("Invalid username or password");
        }

        // Create authorities
        List<GrantedAuthority> authorities = createAuthorities(account);

        // Return authenticated token
        return new UsernamePasswordAuthenticationToken(
                account.getId(),
                null,
                authorities
        );
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }

    private Account findAccountByIdentifier(String identifier) {
        // Try to find by different identifiers
        return accountRepository.findByIdAndActiveIsTrue(identifier)
                .or(() -> accountRepository.findByEmailAndActiveIsTrue(identifier))
                .or(() -> accountRepository.findByPhoneAndActiveIsTrue(identifier))
                .orElse(null);
    }

    private List<GrantedAuthority> createAuthorities(Account account) {
        List<GrantedAuthority> authorities = new ArrayList<>();

        if (account.getRole() != null) {
            authorities.add(new SimpleGrantedAuthority(AccountRoleEnum.ADMIN.name()));
        }

        // If account has specific permissions, add them
        // if (account.getPermissions() != null) {
        //     account.getPermissions().forEach(permission ->
        //         authorities.add(new SimpleGrantedAuthority(permission))
        //     );
        // }

        return authorities;
    }
}