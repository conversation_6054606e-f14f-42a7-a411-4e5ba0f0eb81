package com.nucalale.app.entities;

import com.nucalale.app.enums.AccountImagesType;
import jakarta.persistence.*;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "account_images")
public class AccountImages extends BaseEntity {
    @Column(name = "url")
    private String url;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private AccountImagesType type;

    @JoinColumn(name = "account_id")
    @ManyToOne
    private Account account;
}
