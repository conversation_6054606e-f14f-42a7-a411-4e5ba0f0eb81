package com.nucalale.app.services;

import com.nucalale.app.dto.request.RequestAddEmployeeImages;
import com.nucalale.app.dto.request.RequestCreateEmployee;
import com.nucalale.app.dto.response.ResponseDetailEmployee;
import com.nucalale.app.dto.response.ResponseEmployeeCreated;
import com.nucalale.app.dto.response.ResponseListEmployee;
import com.nucalale.app.enums.EmployeeStatusEnum;
import com.nucalale.app.enums.ResponseEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface EmployeeService {

    ResponseEmployeeCreated createEmployee(RequestCreateEmployee request);

    Page<ResponseListEmployee> getEmployees(Pageable pageable, String agencyId, String query, EmployeeStatusEnum status);

    ResponseEnum resendCredentials(String accountId);

    ResponseDetailEmployee getEmployee(String id);

    ResponseEnum registerEmployeeFace(String id, List<MultipartFile> images);

    ResponseDetailEmployee validateEmployee(MultipartFile image);

    List<ResponseListEmployee> listEmployeeByWorkUnit(String id);

    ResponseEnum activateEmployee(String id);

    List<String> addEmployeeImages(RequestAddEmployeeImages req);
}
