package com.nucalale.app.controllers.impl;

import com.nucalale.app.annotations.BaseControllerImpl;
import com.nucalale.app.controllers.WorkUnitController;
import com.nucalale.app.dto.request.RequestManageWorkUnitLocation;
import com.nucalale.app.dto.request.RequestWorkUnit;
import com.nucalale.app.dto.response.BaseResponse;
import com.nucalale.app.services.WorkUnitService;
import com.nucalale.app.utils.ResponseHelper;

import java.util.List;

@BaseControllerImpl
public class WorkUnitControllerImpl implements WorkUnitController {
    private final WorkUnitService workUnitService;

    public WorkUnitControllerImpl(WorkUnitService workUnitService) {
        this.workUnitService = workUnitService;
    }

    @Override
    public BaseResponse createWorkUnit(RequestWorkUnit req) {
        return ResponseHelper.createBaseResponse(workUnitService.createWorkUnit(req));
    }

    @Override
    public BaseResponse editWorkUnit(RequestWorkUnit req, String id) {
        return ResponseHelper.createBaseResponse(workUnitService.editWorkUnit(req, id));
    }

    @Override
    public BaseResponse manageWorkUnitLocation(String id, List<RequestManageWorkUnitLocation> req) {
        return ResponseHelper.createBaseResponse(workUnitService.manageWorkUnitLocation(id, req) );
    }

    @Override
    public BaseResponse getWorkUnitLocation(String id) {
        return ResponseHelper.createBaseResponse(workUnitService.getWorkUnitLocation(id));
    }
}
