package com.nucalale.app.entities;

import com.nucalale.app.enums.TimeTableTypeEnum;
import jakarta.persistence.*;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "work_unit")
public class WorkUnit extends BaseEntity {
    @Column(name = "name")
    private String name;


    @Column(name = "description")
    private String description;

    @JoinColumn(name = "agency_id")
    @ManyToOne
    private Agency agency;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    TimeTableTypeEnum type;
}
