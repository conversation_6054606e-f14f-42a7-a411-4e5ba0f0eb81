<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
        http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="1.0.7" author="rivo">
        <addColumn tableName="account">
            <column name="agency_id" type="VARCHAR(32)">
                <constraints nullable="true"/>
            </column>
        </addColumn>

        <!-- Add foreign key constraint -->
        <addForeignKeyConstraint
                constraintName="fk_account_agency"
                baseTableName="account"
                baseColumnNames="agency_id"
                referencedTableName="agency"
                referencedColumnNames="id"
                onDelete="SET NULL"/>
    </changeSet>
</databaseChangeLog>
