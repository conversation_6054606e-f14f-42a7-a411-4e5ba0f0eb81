package com.nucalale.app.controllers.impl;

import com.nucalale.app.annotations.BaseControllerImpl;
import com.nucalale.app.controllers.MasterDataController;
import com.nucalale.app.dto.response.BaseResponse;
import com.nucalale.app.services.MasterDataService;
import com.nucalale.app.utils.ResponseHelper;
import lombok.RequiredArgsConstructor;

@BaseControllerImpl
@RequiredArgsConstructor
public class MasterDataControllerImpl implements MasterDataController {

    private final MasterDataService masterDataService;

    @Override
    public BaseResponse getMasterDataGovernmentRank() {
        return ResponseHelper.createBaseResponse(masterDataService.getMasterDataGovernmentRank());
    }

    @Override
    public BaseResponse getListReligion() {
        return ResponseHelper.createBaseResponse(masterDataService.getListReligion());
    }

    @Override
    public BaseResponse getListMaritalStatus() {
        return ResponseHelper.createBaseResponse(masterDataService.getListMaritalStatus());
    }

    @Override
    public BaseResponse checkNik(String nip) {
        return ResponseHelper.createBaseResponse(masterDataService.checkNip(nip));
    }

}
