package com.nucalale.app.entities;


import jakarta.persistence.*;
import lombok.*;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "schedule")
public class Schedule extends BaseEntity {
    @Column(name = "date")
    private Date date;

    @JoinColumn(name = "account_id")
    @ManyToOne
    private Account account;

    @JoinColumn(name = "shift_id")
    @ManyToOne
    private Shift shift;

    @JoinColumn(name = "work_unit_id")
    @ManyToOne
    private WorkUnit workUnit;

}
