<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="1.1.3" author="rivo pelu">
        <createTable tableName="location_work_unit_radius">
            <column name="id" type="VARCHAR(32)">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="latitude" type="double"/>
            <column name="longitude" type="double"/>
            <column name="radius" type="double"/>
            <column name="work_unit_id" type="varchar(255)"/>
            <!--START -->
            <column name="active" type="BOOLEAN"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="created_date" type="TIMESTAMP"/>
            <column name="deleted_by" type="VARCHAR(255)"/>
            <column name="deleted_date" type="TIMESTAMP"/>
            <column name="updated_by" type="VARCHAR(255)"/>
            <column name="updated_date" type="TIMESTAMP"/>
            <!--END -->
        </createTable>

        <addForeignKeyConstraint
                baseTableName="location_work_unit_radius"
                baseColumnNames="work_unit_id"
                constraintName="work_unit_location_radius"
                referencedTableName="work_unit"
                referencedColumnNames="id"
        />
    </changeSet>
</databaseChangeLog>