package com.nucalale.app.configs;

import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.metrics.Meter;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.propagation.ContextPropagators;
import io.opentelemetry.extension.trace.propagation.JaegerPropagator;
import io.opentelemetry.extension.trace.propagation.B3Propagator;
import io.opentelemetry.api.trace.propagation.W3CTraceContextPropagator;
import io.opentelemetry.api.baggage.propagation.W3CBaggagePropagator;
import io.opentelemetry.context.propagation.TextMapPropagator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenTelemetry Configuration for Manual Instrumentation
 * 
 * This configuration class provides beans for manual instrumentation
 * when you need to create custom spans, metrics, or add custom attributes.
 * 
 * The auto-instrumentation via Java agent handles most common scenarios,
 * but this configuration enables advanced custom instrumentation.
 */
@Configuration
@Slf4j
public class OpenTelemetryConfig {

    @Value("${otel.service.name:nuca-lale-backend}")
    private String serviceName;

    @Value("${otel.service.version:unknown}")
    private String serviceVersion;

    /**
     * Provides a Tracer instance for manual span creation
     * 
     * Usage example:
     * @Autowired private Tracer tracer;
     * 
     * Span span = tracer.spanBuilder("custom-operation")
     *     .setAttribute("user.id", userId)
     *     .startSpan();
     * try (Scope scope = span.makeCurrent()) {
     *     // Your business logic here
     * } finally {
     *     span.end();
     * }
     */
    @Bean
    public Tracer tracer() {
        OpenTelemetry openTelemetry = GlobalOpenTelemetry.get();
        log.info("Creating OpenTelemetry Tracer for service: {}", serviceName);
        return openTelemetry.getTracer(serviceName, serviceVersion);
    }

    /**
     * Provides a Meter instance for custom metrics creation
     * 
     * Usage example:
     * @Autowired private Meter meter;
     * 
     * LongCounter counter = meter.counterBuilder("user_login_attempts")
     *     .setDescription("Number of user login attempts")
     *     .setUnit("1")
     *     .build();
     * 
     * counter.add(1, Attributes.of(
     *     AttributeKey.stringKey("login.status"), "success",
     *     AttributeKey.stringKey("user.type"), "employee"
     * ));
     */
    @Bean
    public Meter meter() {
        OpenTelemetry openTelemetry = GlobalOpenTelemetry.get();
        log.info("Creating OpenTelemetry Meter for service: {}", serviceName);
        return openTelemetry.getMeter(serviceName, serviceVersion);
    }

    /**
     * Provides the OpenTelemetry instance for advanced use cases
     */
    @Bean
    public OpenTelemetry openTelemetry() {
        OpenTelemetry openTelemetry = GlobalOpenTelemetry.get();
        log.info("OpenTelemetry instance configured for service: {} version: {}", serviceName, serviceVersion);
        return openTelemetry;
    }

    /**
     * Configure context propagators for distributed tracing
     * This ensures trace context is properly propagated across service boundaries
     */
    @Bean
    public ContextPropagators contextPropagators() {
        return ContextPropagators.create(
            TextMapPropagator.composite(
                W3CTraceContextPropagator.getInstance(),
                W3CBaggagePropagator.getInstance(),
                B3Propagator.injectingSingleHeader(),
                JaegerPropagator.getInstance()
            )
        );
    }
}
