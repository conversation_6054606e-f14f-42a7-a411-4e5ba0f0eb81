package com.nucalale.app.entities;


import jakarta.persistence.*;
import lombok.*;

import java.math.BigInteger;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "location_work_unit_radius")
public class LocationWorkUnitRadius extends BaseEntity {
    @Column(name = "name")
    private String name;

    @Column(name = "latitude")
    private Double latitude;

    @Column(name = "longitude")
    private Double longitude;

    @Column(name = "radius")
    private Integer radius;

    @ManyToOne
    @JoinColumn(name = "work_unit_id")
    private WorkUnit workUnit;

}
