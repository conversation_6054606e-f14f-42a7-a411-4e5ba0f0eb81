{"info": {"_postman_id": "********-39ad44fd-b858-4835-8e0a-015082c1c6d5", "name": "API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Sign In", "event": [{"listen": "test", "script": {"id": "dc54b0d5-7aef-49d3-b74c-d521db84099c", "exec": ["var jsonData = pm.response.json();\r", "var token = jsonData.response_data?.access_token;\r", "console.info({\r", "    \"ACCESS TOKEN\" : token,\r", "})\r", "pm.environment.set(\"access-token\", token);"], "type": "text/javascript", "packages": {}}}], "id": "********-feb6ef12-2815-4f72-bb4b-3e2d4cf1880b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"data\" : \"<EMAIL>\",\n    \"password\"  : \"admin\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/auth/v1/admin/sign-in", "host": ["{{base-url}}"], "path": ["auth", "v1", "admin", "sign-in"]}}, "response": []}], "id": "********-19dfc769-3460-4a39-907c-ff29ed28903a"}, {"name": "Area", "item": [{"name": "List Province", "event": [{"listen": "test", "script": {"id": "bf8d9587-5dc1-4603-9318-6d2796137ae1", "exec": ["pm.test(\"Response status code is 401\", function () {\r", "    pm.expect(pm.response.code).to.equal(401);\r", "});\r", "\r", "\r", "pm.test(\"Response time is less than 200ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "\r", "pm.test(\"Response content type is text/xml\", function () {\r", "    pm.expect(pm.response.headers.get('Content-Type')).to.equal('text/xml');\r", "});\r", "\r", "\r", "pm.test(\"Response body is not null\", function () {\r", "    const responseData = xml2Json(pm.response.text());\r", "    \r", "    pm.expect(responseData).to.exist;\r", "});\r", "\r", "\r", "pm.test(\"Response body matches the expected XML schema\", function () {\r", "    const responseData = xml2Json(pm.response.text());\r", "    \r", "    pm.expect(responseData).to.be.an('object');\r", "    pm.expect(responseData).to.have.property('error'); // Assuming the expected schema has an error property\r", "    pm.expect(responseData.error).to.have.property('code'); // Assuming the expected schema has a code property\r", "    pm.expect(responseData.error).to.have.property('message'); // Assuming the expected schema has a message property\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "id": "********-4c08d05a-5baf-413f-a54e-7c13105ab775", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base-url}}/area/province", "host": ["{{base-url}}"], "path": ["area", "province"]}}, "response": []}, {"name": "City By Province", "id": "********-950b0726-7869-4164-b737-2b2699dcd4dc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{base-url}}/area/city/:id", "host": ["{{base-url}}"], "path": ["area", "city", ":id"], "variable": [{"key": "id", "value": "25"}]}}, "response": []}, {"name": "District By Id", "id": "********-1c3de8c7-5362-4689-bb11-0ff3855bfc66", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{base-url}}/area/district/:id", "host": ["{{base-url}}"], "path": ["area", "district", ":id"], "variable": [{"key": "id", "value": "379"}]}}, "response": []}, {"name": "Sub District", "id": "********-c6a117c5-eacd-46ea-8d63-d92f35a30937", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{base-url}}/area/sub-district/:id", "host": ["{{base-url}}"], "path": ["area", "sub-district", ":id"], "variable": [{"key": "id", "value": "5260"}]}}, "response": []}], "id": "********-d8cc5fbe-3744-415e-be8d-d28f97afb49b"}, {"name": "Account", "item": [{"name": "Me", "id": "********-f0728672-1563-402f-988e-0aca951f8815", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base-url}}/account/v1/me", "host": ["{{base-url}}"], "path": ["account", "v1", "me"]}}, "response": []}], "id": "********-9f02080a-44b9-4bd5-a70a-7b53390fb364"}, {"name": "master-data", "item": [{"name": "List government rank", "event": [{"listen": "test", "script": {"id": "cba43a06-5622-4496-af95-3a42c7baf497", "exec": ["var template = `\r", "<style type=\"text/css\">\r", "    .tftable {font-size:14px;color:#333333;width:100%;border-width: 1px;border-color: #87ceeb;border-collapse: collapse;}\r", "    .tftable th {font-size:18px;background-color:#87ceeb;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;text-align:left;}\r", "    .tftable tr {background-color:#ffffff;}\r", "    .tftable td {font-size:14px;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;}\r", "    .tftable tr:hover {background-color:#e0ffff;}\r", "</style>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>Label</th>\r", "        <th>Value</th>\r", "    </tr>\r", "    \r", "    {{#each response.response_data}}\r", "        <tr>\r", "            <td>{{label}}</td>\r", "            <td>{{value}}</td>\r", "        </tr>\r", "    {{/each}}\r", "</table>\r", "`;\r", "\r", "function constructVisualizerPayload() {\r", "    return {response: pm.response.json()};\r", "}\r", "\r", "pm.visualizer.set(template, constructVisualizerPayload());\r", "var responseJSON = pm.response.json();\r", "\r", "// Test for response status code\r", "pm.test(\"Response status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Test for response time\r", "pm.test(\"Response time is less than 200ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(200);\r", "});\r", "\r", "// Test for response body structure\r", "pm.test(\"Response body contains success and response_data properties\", function () {\r", "    pm.expect(responseJSON).to.have.property('success').that.is.a('boolean');\r", "    pm.expect(responseJSON).to.have.property('response_data').that.is.an('array');\r", "    \r", "    // Check each element in the response_data array\r", "    responseJSON.response_data.forEach(function(item) {\r", "        pm.expect(item).to.have.property('label').that.is.a('string');\r", "        pm.expect(item).to.have.property('value').that.is.a('string');\r", "    });\r", "});"], "type": "text/javascript", "packages": {}}}], "id": "********-02e8cec2-f6af-412d-bf21-9f50c4581a8e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base-url}}/master-data/v1/government-rank", "host": ["{{base-url}}"], "path": ["master-data", "v1", "government-rank"]}}, "response": []}, {"name": "List religion", "id": "********-0f511aec-8a9f-4514-8751-211fa251128d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base-url}}/master-data/v1/religion", "host": ["{{base-url}}"], "path": ["master-data", "v1", "religion"]}}, "response": []}, {"name": "List Marital Status", "id": "********-2926826e-6b64-43fe-a2d1-b650539ca7c3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base-url}}/master-data/v1/marital-status", "host": ["{{base-url}}"], "path": ["master-data", "v1", "marital-status"]}}, "response": []}], "id": "********-fbfa7bdd-4887-4283-9345-a59084ee76b9"}, {"name": "Agency", "item": [{"name": "Create Agency", "id": "********-fa5b99bd-645e-4f9a-ba08-76b352d9065a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"lat\": 1.5195527,\r\n    \"lng\": 124.8869022,\r\n    \"name\": \"<PERSON><PERSON>\",\r\n    \"logo_url\": \"https://maxdjzqweveqptppmydq.supabase.co/storage/v1/object/public/smart-school/AGENCY_IMAGE/1749293748670_upload.png\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/agency/v1", "host": ["{{base-url}}"], "path": ["agency", "v1"]}}, "response": []}, {"name": "Update Agency", "id": "********-cbe97e0e-7fea-4a01-8899-42872f9f1cce", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"lat\": 1.5195527,\r\n    \"lng\": 124.8869022,\r\n    \"name\": \"<PERSON><PERSON>\",\r\n    \"logo_url\": \"https://maxdjzqweveqptppmydq.supabase.co/storage/v1/object/public/smart-school/AGENCY_IMAGE/1749293748670_upload.png\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/agency/v1/:id", "host": ["{{base-url}}"], "path": ["agency", "v1", ":id"], "variable": [{"key": "id", "value": "ADA3FDFD91E54742B08C176BC9120B0E"}]}}, "response": []}, {"name": "Edit Agency", "id": "********-942e6a16-0249-4248-a1ce-34d1d7ecbf6b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"lat\": 1.5195527,\r\n    \"lng\": 124.8869022,\r\n    \"name\": \"<PERSON><PERSON> edit\",\r\n    \"logo_url\": \"https://maxdjzqweveqptppmydq.supabase.co/storage/v1/object/public/smart-school/AGENCY_IMAGE/1749293748670_upload.png\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/agency/v1/edit/:id", "host": ["{{base-url}}"], "path": ["agency", "v1", "edit", ":id"], "variable": [{"key": "id", "value": "adsf"}]}}, "response": []}, {"name": "Detail Agency", "id": "********-c959f0ba-9897-40f0-90af-87a2ef5235b4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"lat\": 1.5195527,\r\n    \"lng\": 124.8869022,\r\n    \"name\": \"<PERSON><PERSON>\",\r\n    \"logo_url\": \"https://maxdjzqweveqptppmydq.supabase.co/storage/v1/object/public/smart-school/AGENCY_IMAGE/1749293748670_upload.png\",\r\n    \"checked\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/agency/v1/:id", "host": ["{{base-url}}"], "path": ["agency", "v1", ":id"], "variable": [{"key": "id", "value": "ADA3FDFD91E54742B08C176BC9120B0E"}]}}, "response": []}, {"name": "List Agency", "id": "********-a92ffe29-ca15-4832-b485-23742ebe8eba", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"lat\": 1.5195527,\r\n    \"lng\": 124.8869022,\r\n    \"name\": \"<PERSON><PERSON>\",\r\n    \"logo_url\": \"https://maxdjzqweveqptppmydq.supabase.co/storage/v1/object/public/smart-school/AGENCY_IMAGE/1749293748670_upload.png\",\r\n    \"checked\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/agency/v1", "host": ["{{base-url}}"], "path": ["agency", "v1"]}}, "response": []}], "id": "********-0390bf8d-5251-4ea5-97fa-9fc758b840c2"}, {"name": "Employe", "item": [{"name": "Create Employe", "id": "********-0ffc569b-6368-402f-9a5c-500f9d4ab1ad", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"akmal\",\r\n    \"phone\": \"62872123223212\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"nip\": \"767216736761273671\",\r\n    \"role\": \"USER\",\r\n    \"agency_id\": \"47AABDD278A94654A53265D2940A877D\",\r\n    \"work_unit_id\" : \"717B40610B6047F19FBE66C46CDB2A36\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/employee/v1", "host": ["{{base-url}}"], "path": ["employee", "v1"]}}, "response": []}, {"name": "List Employe", "id": "********-c9044500-2370-4fed-b74d-ccda3a165a61", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "\r\n{\r\n    \"name\": \"<PERSON>\",\r\n    \"phone\": \"************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"nip\": \"198501012010011001\",\r\n    \"role\": \"USER\",\r\n    \"gender\": \"MALE\",\r\n    \"agency_id\": \"ABC123DEF456\",\r\n    \"government_rank\": \"III/a\",\r\n    \"religion\": \"Islam\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/employee/v1", "host": ["{{base-url}}"], "path": ["employee", "v1"], "query": [{"key": "agency_id", "value": "ADA3FDFD91E54742B08C176BC9120B0E", "disabled": true}]}}, "response": []}, {"name": "Detail Employee", "id": "********-313cfb06-043c-4b21-a91c-574c4b69e9f9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "\r\n{\r\n    \"name\": \"<PERSON>\",\r\n    \"phone\": \"************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"nip\": \"198501012010011001\",\r\n    \"role\": \"USER\",\r\n    \"gender\": \"MALE\",\r\n    \"agency_id\": \"ABC123DEF456\",\r\n    \"government_rank\": \"III/a\",\r\n    \"religion\": \"Islam\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/employee/v1/:id", "host": ["{{base-url}}"], "path": ["employee", "v1", ":id"], "query": [{"key": "agency_id", "value": "ADA3FDFD91E54742B08C176BC9120B0E", "disabled": true}], "variable": [{"key": "id", "value": "B04332BCCAB743CDA48B27459AA698BF"}]}}, "response": []}, {"name": "Resend Employe Credential", "id": "********-ab6f60b0-a2ef-4cbb-aefa-1c59e1c066fd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PATCH", "header": [], "body": {"mode": "raw", "raw": "\r\n{\r\n    \"name\": \"<PERSON>\",\r\n    \"phone\": \"************\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"nip\": \"198501012010011001\",\r\n    \"role\": \"USER\",\r\n    \"gender\": \"MALE\",\r\n    \"agency_id\": \"ABC123DEF456\",\r\n    \"government_rank\": \"III/a\",\r\n    \"religion\": \"Islam\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/employee/v1/:accountid/resend-credentials", "host": ["{{base-url}}"], "path": ["employee", "v1", ":accountid", "resend-credentials"], "variable": [{"key": "accountid", "value": "B2592BF32F2046A0AE4174378250B8C1"}]}}, "response": []}, {"name": "Register Employee Face", "id": "********-cdff5cf0-9b5f-4dca-8d4d-1cd238580157", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "images", "type": "file", "uuid": "5396f021-a974-45c7-8206-ee0260472d1e", "src": ["/Users/<USER>/Documents/presensi-nuca-lale-project/face-recognation-service-nuca-lale/real_data/radit 1.png", "/Users/<USER>/Documents/presensi-nuca-lale-project/face-recognation-service-nuca-lale/real_data/radit 2.png", "/Users/<USER>/Documents/presensi-nuca-lale-project/face-recognation-service-nuca-lale/real_data/radit 3.png", "/Users/<USER>/Documents/presensi-nuca-lale-project/face-recognation-service-nuca-lale/real_data/radit 4.png"]}]}, "url": {"raw": "{{base-url}}/employee/v1/register-face/:id", "host": ["{{base-url}}"], "path": ["employee", "v1", "register-face", ":id"], "variable": [{"key": "id", "value": "B04332BCCAB743CDA48B27459AA698BF"}]}}, "response": []}, {"name": "Validate User", "id": "********-*************-4de8-a757-097ca9cb66d4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "image", "type": "file", "uuid": "5396f021-a974-45c7-8206-ee0260472d1e", "src": "/C:/Users/<USER>/Downloads/92528810.jpeg"}]}, "url": {"raw": "{{base-url}}/employee/v1/validate", "host": ["{{base-url}}"], "path": ["employee", "v1", "validate"]}}, "response": []}], "id": "********-b07099e3-9720-46c6-8602-9b26dbddf3e0"}, {"name": "Schedule", "item": [{"name": "Work Unit", "item": [{"name": "Create Work Unit", "id": "********-2035efaf-72b6-4581-b0e0-532fab6d0da6", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\" : \"with timetable\",\r\n    \"description\" : \"ini description dengan timetable\",\r\n    \"agency_id\" : \"5601E4B86B564B2F9DE7FC13CC65AAB9\",\r\n    \"timetable\" : [\r\n       {\r\n        \"day\" : 1,\r\n        \"start_time\" : \"07:00\",\r\n        \"end_time\" : \"08:00\",\r\n        \"name\" : \"Clock in\",\r\n        \"description\" : \"<PERSON>bsen Page\"\r\n       },\r\n        {\r\n        \"day\" : 1,\r\n        \"start_time\" : \"12:00\",\r\n        \"end_time\" : \"13:00\",\r\n        \"name\" : \"in day\",\r\n        \"description\" : \"Absen setelah istirahat\"\r\n       },\r\n        {\r\n        \"day\" : 1,\r\n        \"start_time\" : \"17:00\",\r\n        \"end_time\" : \"18:00\",\r\n        \"name\" : \"clock out\",\r\n        \"description\" : \"Absen pulang\"\r\n       },\r\n        {\r\n        \"day\" : 2,\r\n        \"start_time\" : \"07:00\",\r\n        \"end_time\" : \"08:00\",\r\n        \"name\" : \"Clock in\",\r\n        \"description\" : \"Absen Page\"\r\n       },\r\n        {\r\n        \"day\" : 2,\r\n        \"start_time\" : \"12:00\",\r\n        \"end_time\" : \"13:00\",\r\n        \"name\" : \"in day\",\r\n        \"description\" : \"<PERSON><PERSON><PERSON> setelah istirahat\"\r\n       },\r\n        {\r\n        \"day\" : 2,\r\n        \"start_time\" : \"17:00\",\r\n        \"end_time\" : \"18:00\",\r\n        \"name\" : \"clock out\",\r\n        \"description\" : \"Absen pulang\"\r\n       }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/schedule/v1/work-unit", "host": ["{{base-url}}"], "path": ["schedule", "v1", "work-unit"]}}, "response": []}, {"name": "Update Work Unit", "id": "********-eb780e64-dc2e-40ce-a96b-36921ab60a4c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\" : \"name diedit\",\r\n    \"description\" : \"Description\",\r\n    \"agency_id\" : \"F7BC198315C24C8AA0D4034658E25091\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/schedule/v1/work-unit/:id", "host": ["{{base-url}}"], "path": ["schedule", "v1", "work-unit", ":id"], "variable": [{"key": "id", "value": "528B68829E1644D3ACEDE88354EB82D3"}]}}, "response": []}, {"name": "List work unit by agency id", "id": "********-801937eb-0e67-4682-ba74-dfb1702de884", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\" : \"name\",\r\n    \"description\" : \"Description\",\r\n    \"agency_id\" : \"5601E4B86B564B2F9DE7FC13CC65AAB9\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/schedule/v1/work-unit/agency/:agencyId", "host": ["{{base-url}}"], "path": ["schedule", "v1", "work-unit", "agency", ":agencyId"], "variable": [{"key": "agencyId", "value": "F7BC198315C24C8AA0D4034658E25091"}]}}, "response": []}, {"name": "List Work Unit Pageable", "id": "********-2581d4d9-8655-4107-b4e7-f0b2780a98ea", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\" : \"name\",\r\n    \"description\" : \"Description\",\r\n    \"agency_id\" : \"5601E4B86B564B2F9DE7FC13CC65AAB9\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/schedule/v1/work-unit", "host": ["{{base-url}}"], "path": ["schedule", "v1", "work-unit"], "query": [{"key": "agency_id", "value": "F7BC198315C24C8AA0D4034658E25091", "disabled": true}, {"key": "page", "value": "0", "disabled": true}, {"key": "size", "value": "1", "disabled": true}]}}, "response": []}, {"name": "Detail Work unit", "id": "********-86ba9eca-ece9-4633-ac64-c3001a00f03a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"name\" : \"name\",\r\n    \"description\" : \"Description\",\r\n    \"agency_id\" : \"5601E4B86B564B2F9DE7FC13CC65AAB9\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/schedule/v1/work-unit/detail/:agencyId", "host": ["{{base-url}}"], "path": ["schedule", "v1", "work-unit", "detail", ":agencyId"], "variable": [{"key": "agencyId", "value": "528B68829E1644D3ACEDE88354EB82D3"}]}}, "response": []}], "id": "********-cefd23e8-d1f6-41e5-8fb7-a46bba905b93"}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Create Schedule", "id": "********-32e300e3-8351-4a3f-bac0-0a89200f8254", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"date\" : \"2025-06-12\",\n    \"account_id\" : \"\",\n    \"work_unit_id\" : \"\",\n    \"time_table_id\" : \"\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}], "id": "********-2e269a8c-81f7-4749-a31b-a59c5950bc4a"}, {"name": "Shift", "item": [{"name": "Create Shift", "id": "********-19e65edf-4bd0-4c39-b2f6-b86ea1eca54b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"agency_id\": \"47AABDD278A94654A53265D2940A877D\",\r\n    \"work_unit_id\": \"717B40610B6047F19FBE66C46CDB2A36\",\r\n    \"times\": [\r\n        {\r\n            \"name\": \"Shift 2\",\r\n            \"start_time\": \"14:00:00\",\r\n            \"end_time\": \"19:00:00\",\r\n            \"time_tables\": [\r\n                {\r\n                    \"name\": \"Jam Masuk\",\r\n                    \"start_time\": \"14:00:00\",\r\n                    \"end_time\": \"14:30:00\"\r\n                },\r\n                {\r\n                    \"name\": \"Jam Pulang\",\r\n                    \"start_time\": \"19:00:00\",\r\n                    \"end_time\": \"19:30:00\"\r\n                }\r\n            ]\r\n        },\r\n         {\r\n            \"name\": \"Shift 3\",\r\n            \"start_time\": \"19:00:00\",\r\n            \"end_time\": \"24:00:00\",\r\n            \"time_tables\": [\r\n                {\r\n                    \"name\": \"Jam Masuk\",\r\n                    \"start_time\": \"19:00:00\",\r\n                    \"end_time\": \"19:30:00\"\r\n                },\r\n                {\r\n                    \"name\": \"Jam Pulang\",\r\n                    \"start_time\": \"24:00:00\",\r\n                    \"end_time\": \"00:30:00\"\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/schedule/v1/create-shift", "host": ["{{base-url}}"], "path": ["schedule", "v1", "create-shift"], "query": [{"key": "agency_id", "value": "F7BC198315C24C8AA0D4034658E25091", "disabled": true}, {"key": "page", "value": "0", "disabled": true}, {"key": "size", "value": "1", "disabled": true}]}}, "response": []}, {"name": "Detail Shift", "id": "********-e1f6fd7b-b062-427b-8a51-b0f8925a6647", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"agency_id\": \"47AABDD278A94654A53265D2940A877D\",\r\n    \"work_unit_id\": \"717B40610B6047F19FBE66C46CDB2A36\",\r\n    \"times\": [\r\n        {\r\n            \"name\": \"Shift 1\",\r\n            \"start_time\": \"10:00:00\",\r\n            \"end_time\": \"14:00:00\",\r\n            \"time_tables\": [\r\n                {\r\n                    \"name\": \"Jam Masuk\",\r\n                    \"start_time\": \"10:00:00\",\r\n                    \"end_time\": \"10:30:00\"\r\n                },\r\n                {\r\n                    \"name\": \"Jam Pulang\",\r\n                    \"start_time\": \"14:00:00\",\r\n                    \"end_time\": \"14:30:00\"\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/schedule/v1/shift/:id", "host": ["{{base-url}}"], "path": ["schedule", "v1", "shift", ":id"], "query": [{"key": "agency_id", "value": "F7BC198315C24C8AA0D4034658E25091", "disabled": true}, {"key": "page", "value": "0", "disabled": true}, {"key": "size", "value": "1", "disabled": true}], "variable": [{"key": "id", "value": "D5A9F5C29284457BAAD834939288A6A6"}]}}, "response": []}, {"name": "List Shift By Work Unit", "id": "********-0ac978e5-61b7-499b-a76e-2c3c9aa30e8b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"agency_id\": \"47AABDD278A94654A53265D2940A877D\",\r\n    \"work_unit_id\": \"717B40610B6047F19FBE66C46CDB2A36\",\r\n    \"times\": [\r\n        {\r\n            \"name\": \"Shift 1\",\r\n            \"start_time\": \"10:00:00\",\r\n            \"end_time\": \"14:00:00\",\r\n            \"time_tables\": [\r\n                {\r\n                    \"name\": \"Jam Masuk\",\r\n                    \"start_time\": \"10:00:00\",\r\n                    \"end_time\": \"10:30:00\"\r\n                },\r\n                {\r\n                    \"name\": \"Jam Pulang\",\r\n                    \"start_time\": \"14:00:00\",\r\n                    \"end_time\": \"14:30:00\"\r\n                }\r\n            ]\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/schedule/v1/shifts-by-work-unit/:id", "host": ["{{base-url}}"], "path": ["schedule", "v1", "shifts-by-work-unit", ":id"], "query": [{"key": "page", "value": "0", "disabled": true}, {"key": "size", "value": "1", "disabled": true}], "variable": [{"key": "id", "value": "717B40610B6047F19FBE66C46CDB2A36"}]}}, "response": []}], "id": "********-4d74ff46-91be-4131-a585-98f46eabef8f"}], "id": "********-b5cc7588-b5e9-440b-b534-896ceb8138b3"}, {"name": "Attendance", "item": [{"name": "List user timetable", "id": "********-4eca856e-ac59-4257-93b5-da463aa10ebf", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base-url}}/attendance/v1/user-time-table", "host": ["{{base-url}}"], "path": ["attendance", "v1", "user-time-table"]}}, "response": []}], "id": "********-54a4f618-3dd0-4814-8bc8-4073935e8f37"}, {"name": "<PERSON>", "id": "********-c1eedb97-fdcd-4b56-b027-728fe81256eb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{base-url}}/ping", "host": ["{{base-url}}"], "path": ["ping"]}}, "response": []}]}