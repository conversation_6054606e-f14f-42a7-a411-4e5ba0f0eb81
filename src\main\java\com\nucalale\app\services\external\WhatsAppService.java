package com.nucalale.app.services.external;

import com.nucalale.app.dto.external.WhatsAppMessageRequest;
import com.nucalale.app.dto.external.WhatsAppMessageResponse;

public interface WhatsAppService {
    
    /**
     * Send WhatsApp message via Fonnte API
     * @param request WhatsApp message request
     * @return WhatsApp message response
     */
    WhatsAppMessageResponse sendMessage(WhatsAppMessageRequest request);
    
    /**
     * Send employee credentials via WhatsApp
     * @param phoneNumber Employee phone number
     * @param employeeName Employee name
     * @param nip Employee NIP
     * @param temporaryPassword Temporary password
     * @return WhatsApp message response
     */
    WhatsAppMessageResponse sendEmployeeCredentials(String phoneNumber, String employeeName, String nip, String temporaryPassword);
}
