package com.nucalale.app.entities;

import com.nucalale.app.enums.OtpAndTokenTypeEnum;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "otp_and_token")
public class OtpAndToken {
    @Id
    private String id;

    @Column(name = "data")
    private String data;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private OtpAndTokenTypeEnum type;

    @ManyToOne
    @JoinColumn(name = "account_id")
    private Account account;

    @Column(name = "expired_date")
    private Long expiredDate;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @PrePersist
    public void prePersist() {
        if (this.id == null) {
            this.id = UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
            this.createdDate = LocalDateTime.now();
        }
    }
}
