<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="1.1.7" author="rivopelu">
        <createTable tableName="shift">
            <column name="id" type="VARCHAR(32)">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="varchar(255)"/>
            <column name="description" type="varchar(255)"/>
            <column name="start_time" type="time"/>
            <column name="end_time" type="time"/>
            <column name="work_unit_id" type="varchar(36)"/>
            <!-- base entity fields -->
            <column name="active" type="BOOLEAN"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="created_date" type="TIMESTAMP"/>
            <column name="deleted_by" type="VARCHAR(255)"/>
            <column name="deleted_date" type="TIMESTAMP"/>
            <column name="updated_by" type="VARCHAR(255)"/>
            <column name="updated_date" type="TIMESTAMP"/>
        </createTable>
        <addForeignKeyConstraint baseTableName="shift" baseColumnNames="work_unit_id"
                                 constraintName="shift_work_unit_fk" referencedTableName="work_unit"
                                 referencedColumnNames="id"/>
    </changeSet>
</databaseChangeLog>