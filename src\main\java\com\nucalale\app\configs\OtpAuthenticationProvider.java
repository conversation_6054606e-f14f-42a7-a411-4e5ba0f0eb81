package com.nucalale.app.configs;

import com.nucalale.app.entities.Account;
import com.nucalale.app.entities.OtpAndToken;
import com.nucalale.app.enums.OtpAndTokenTypeEnum;
import com.nucalale.app.repositories.AccountRepository;
import com.nucalale.app.repositories.OtpAndTokenRepository;
import com.nucalale.app.utils.OtpAuthenticationToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class OtpAuthenticationProvider implements AuthenticationProvider {

    private final OtpAndTokenRepository otpAndTokenRepository;
    private final AccountRepository accountRepository;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String accountId = authentication.getPrincipal().toString();
        String otp = authentication.getCredentials().toString();

        log.debug("Attempting OTP authentication for account: {}", accountId);
        log.debug("Provided OTP: {}", otp);

        // Validasi account
        Account account = accountRepository.findById(accountId)
                .orElseThrow(() -> new BadCredentialsException("Account not found"));

        // Cari OTP record
        Optional<OtpAndToken> otpRecord = otpAndTokenRepository.findByAccountAndType(account, OtpAndTokenTypeEnum.SIGN_IN_USER_OTP);

        if (otpRecord.isEmpty()) {
            log.error("OTP record not found for account: {}", accountId);
            throw new BadCredentialsException("OTP not found");
        }

        OtpAndToken foundOtp = otpRecord.get();
        log.debug("Found OTP record with value: {}", foundOtp.getData());
        log.debug("OTP expired date: {}, Current time: {}", foundOtp.getExpiredDate(), System.currentTimeMillis());

        // Validasi expired
        if (foundOtp.getExpiredDate() < System.currentTimeMillis()) {
            log.error("OTP expired for account: {}", accountId);
            throw new BadCredentialsException("OTP expired");
        }

        String storedOtp = foundOtp.getData().trim();
        String providedOtp = otp.trim();
        
        if (!storedOtp.equals(providedOtp)) {
            log.error("Invalid OTP for account: {}. Expected: '{}', Provided: '{}'", accountId, storedOtp, providedOtp);
            throw new BadCredentialsException("Invalid OTP");
        }

        log.info("OTP authentication successful for account: {}", accountId);
        
        otpAndTokenRepository.delete(foundOtp);
        
        OtpAuthenticationToken authenticatedToken = new OtpAuthenticationToken(accountId, otp);
        authenticatedToken.setAuthenticated(true);
        return authenticatedToken;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return OtpAuthenticationToken.class.isAssignableFrom(authentication);
    }
}