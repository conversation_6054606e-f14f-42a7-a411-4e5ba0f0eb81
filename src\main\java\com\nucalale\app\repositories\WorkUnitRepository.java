package com.nucalale.app.repositories;


import com.nucalale.app.entities.WorkUnit;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface WorkUnitRepository extends JpaRepository<WorkUnit, String> {
    List<WorkUnit> findByAgencyId(String agencyId, Sort sort);

    @Query("""
                 SELECT w FROM WorkUnit w\s
                 WHERE (:agencyId IS NULL OR w.agency.id = :agencyId)
                   AND (:query IS NULL OR :query = '' OR LOWER(w.name) LIKE LOWER(CONCAT('%', :query, '%')))
                 ORDER BY w.createdDate DESC
            \s""")
    Page<WorkUnit> findAndFilterWorkUnit(
            Pageable pageable,
            @Param("agencyId") String agencyId,
            @Param("query") String query
    );

    Optional<WorkUnit> findByIdAndActiveIsTrue(String id);

    Optional<WorkUnit> findOneByIdAndActiveIsTrue(String workUnitId);
}
