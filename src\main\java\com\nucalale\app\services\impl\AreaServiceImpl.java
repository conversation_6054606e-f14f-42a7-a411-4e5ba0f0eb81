package com.nucalale.app.services.impl;

import com.nucalale.app.dto.response.ResponseAreaData;
import com.nucalale.app.entities.City;
import com.nucalale.app.entities.District;
import com.nucalale.app.entities.Province;
import com.nucalale.app.entities.SubDistrict;
import com.nucalale.app.repositories.CityRepository;
import com.nucalale.app.repositories.DistrictRepository;
import com.nucalale.app.repositories.ProvinceRepository;
import com.nucalale.app.repositories.SubDistrictRepository;
import com.nucalale.app.services.AreaService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.List;

@Service
@RequiredArgsConstructor
public class AreaServiceImpl implements AreaService {
    private final ProvinceRepository provinceRepository;
    private final CityRepository cityRepository;
    private final DistrictRepository districtRepository;
    private final SubDistrictRepository subDistrictRepository;

    @Override
    public List<ResponseAreaData> listProvince() {
        List<Province> provinces = provinceRepository.findAll();
        return provinces.stream().map(e -> ResponseAreaData.builder()
                .id(e.getId())
                .name(e.getName())
                .build()).toList();
    }

    @Override
    public List<ResponseAreaData> listCity(BigInteger id) {
        List<City> cityList = cityRepository.findByProvinceId(id);
        return cityList.stream().map(e -> ResponseAreaData.builder()
                .id(e.getId())
                .name(e.getName())
                .build()).toList();
    }

    @Override
    public List<ResponseAreaData> listDistrict(BigInteger id) {
        List<District> districts = districtRepository.findByCityId(id);
        return districts.stream().map(e -> ResponseAreaData.builder()
                .id(e.getId())
                .name(e.getName())
                .build()).toList();
    }

    @Override
    public List<ResponseAreaData> listSubDistrict(BigInteger id) {
        List<SubDistrict> subDistrictList = subDistrictRepository.findByDistrictId(id);
        return subDistrictList.stream().map(e -> ResponseAreaData.builder()
                .id(e.getId())
                .name(e.getName())
                .postalCode(e.getPostalCode())
                .build()).toList();
    }

    @Override
    public List<ResponseAreaData> listCityBounce(String query) {
        List<City> cityList = cityRepository.findNameBounce(query);
        return cityList.stream().map(e -> ResponseAreaData.builder()
                .id(e.getId())
                .name(e.getName())
                .build()).toList();
    }
}
