package com.nucalale.app.controllers;

import com.nucalale.app.annotations.BaseController;
import com.nucalale.app.dto.request.RequestCreateEditSchedule;
import com.nucalale.app.dto.request.RequestCreateShift;
import com.nucalale.app.dto.request.RequestCreateWorkUnit;
import com.nucalale.app.dto.response.BaseResponse;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

@BaseController("schedule")
public interface ScheduleController {

    @PostMapping("v1/work-unit")
    BaseResponse createWorkUnit(@RequestBody RequestCreateWorkUnit req);

    @GetMapping("v1/work-unit")
    BaseResponse getWorkUnit(Pageable pageable,
                             @RequestParam(value = "agency_id", required = false) String agencyId,
                             @RequestParam(value = "q", required = false) String query
    );

    @GetMapping("v1/work-unit/agency/{agencyId}")
    BaseResponse getWorkUnitByAgencyId(@PathVariable("agencyId") String agencyId);

    @GetMapping("v1/work-unit/detail/{id}")
    BaseResponse getWorkUnitDetail(@PathVariable("id") String workUnitId);

    @PutMapping("v1/work-unit/{id}")
    BaseResponse updateWorkUnit(@PathVariable("id") String workUnitId, @RequestBody RequestCreateWorkUnit req);

    @PostMapping("v1/")
    BaseResponse createEditSchedule(@RequestBody RequestCreateEditSchedule req);

    @PostMapping("v1/create-shift")
    BaseResponse createShift(@RequestBody RequestCreateShift req);

    @GetMapping("v1/shift/{id}")
    BaseResponse getShift(@PathVariable("id") String id);

    @GetMapping("v1/shifts-by-work-unit/{workUnitId}")
    BaseResponse getShiftsByWorkUnitId(@PathVariable("workUnitId") String workUnitId);

}
