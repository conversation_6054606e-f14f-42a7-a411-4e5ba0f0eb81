package com.nucalale.app.services.impl;

import com.nucalale.app.dto.external.WhatsAppMessageRequest;
import com.nucalale.app.dto.request.RequestEmployeeRegister;
import com.nucalale.app.dto.request.RequestRegisterOtherData;
import com.nucalale.app.dto.request.RequestSignIn;
import com.nucalale.app.dto.request.RequestSignInUser;
import com.nucalale.app.dto.response.ResponseAccountData;
import com.nucalale.app.dto.response.ResponseOtpAndToken;
import com.nucalale.app.dto.response.ResponseRegisterEmployee;
import com.nucalale.app.dto.response.ResponseSignIn;
import com.nucalale.app.entities.*;
import com.nucalale.app.enums.*;
import com.nucalale.app.exceptions.BadRequestException;
import com.nucalale.app.exceptions.NotFoundException;
import com.nucalale.app.exceptions.SystemErrorException;
import com.nucalale.app.repositories.*;
import com.nucalale.app.services.AccountService;
import com.nucalale.app.services.AuthService;
import com.nucalale.app.services.JwtService;
import com.nucalale.app.services.external.WhatsAppService;
import com.nucalale.app.utils.EntityUtils;
import com.nucalale.app.utils.PhoneUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import static com.nucalale.app.utils.UtilsHelper.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final AccountRepository accountRepository;
    private final AuthenticationManager authenticationManager;
    private final JwtService jwtService;
    private final OtpAndTokenRepository otpAndTokenRepository;
    private final WhatsAppService whatsAppService;
    private final WorkUnitRepository workUnitRepository;
    private final PasswordEncoder passwordEncoder;
    private final AccountService accountService;
    private final SubDistrictRepository subDistrictRepository;
    private final CityRepository cityRepository;

    @Override
    public ResponseSignIn signInAdmin(RequestSignIn req) {
        Account account = accountRepository.findByEmailAndActiveIsTrue(req.getData()).orElseThrow(() -> new BadRequestException(ResponseEnum.SIGN_IN_FAILED));
        return buildSign(account, req.getPassword());
    }

    @Override
    public ResponseOtpAndToken requestOtpSingInUser(RequestSignInUser requestSignIn) {
        Account account = accountRepository.findByPhoneAndActiveIsTrue(PhoneUtils.checkPhoneNumber(requestSignIn.getPhone()))
                .orElseThrow(() -> new BadRequestException(ResponseEnum.ACCOUNT_NOT_FOUND));

        Optional<OtpAndToken> existingOtp = otpAndTokenRepository.findByAccountAndType(account, OtpAndTokenTypeEnum.SIGN_IN_USER_OTP);

        String otp = generateOTP(4);

        OtpAndToken otpAndToken = OtpAndToken.builder()
                .type(OtpAndTokenTypeEnum.SIGN_IN_USER_OTP)
                .account(account)
                .expiredDate(generateExpiredTimeInMillis(1))
                .data(otp)
                .build();

        existingOtp.ifPresent(existing -> otpAndToken.setId(existing.getId()));

        otpAndTokenRepository.save(otpAndToken);

        log.info("OTP generated for user: {} ({})", account.getName(), account.getPhone());

        if (requestSignIn.isSendWa()) {
            try {
                String message = buildOtpMessage(account.getName(), otp);
                whatsAppService.sendMessage(com.nucalale.app.dto.external.WhatsAppMessageRequest.builder()
                        .target(PhoneUtils.checkPhoneNumber(requestSignIn.getPhone()))
                        .message(message)
                        .countryCode("62")
                        .build());

                log.info("OTP sent via WhatsApp to: {}", account.getPhone());
            } catch (Exception e) {
                log.error("Failed to send OTP via WhatsApp to: {}", account.getPhone(), e);

            }
        } else {
            log.info("OTP for {}: {}", account.getPhone(), otp);
        }

        return ResponseOtpAndToken.builder()
                .accountId(account.getId())
                .data(account.getId())
                .build();
    }

    @Override
    public ResponseSignIn signInUserWithOtp(RequestSignInUser requestSignIn) {
        Account account = accountRepository.findByPhoneAndActiveIsTrue(PhoneUtils.checkPhoneNumber(requestSignIn.getPhone()))
                .orElseThrow(() -> new BadRequestException(ResponseEnum.ACCOUNT_NOT_FOUND));

        OtpAndToken otpRecord = otpAndTokenRepository.findByAccountAndType(account, OtpAndTokenTypeEnum.SIGN_IN_USER_OTP)
                .orElseThrow(() -> new BadRequestException(ResponseEnum.INVALID_OTP));

        if (!otpRecord.getData().equals(requestSignIn.getOtp())) {
            log.warn("Invalid OTP attempt for user: {} ({})", account.getName(), account.getPhone());
            throw new BadRequestException(ResponseEnum.INVALID_OTP);
        }

        if (System.currentTimeMillis() > otpRecord.getExpiredDate()) {
            log.warn("Expired OTP attempt for user: {} ({})", account.getName(), account.getPhone());
            throw new BadRequestException(ResponseEnum.OTP_EXPIRE);
        }

        otpAndTokenRepository.delete(otpRecord);

        log.info("Successful OTP login for user: {} ({})", account.getName(), account.getPhone());

        return buildSignInResponse(account);
    }

    @Override
    public ResponseRegisterEmployee signUpUser(RequestEmployeeRegister req) {

        Optional<Account> findPhone = accountRepository.findByPhoneAndActiveIsTrue(PhoneUtils.checkPhoneNumber(req.getPhone()));

        if (findPhone.isPresent()) {
            throw new BadRequestException(ResponseEnum.PHONE_ALREADY_EXIST);
        }

        String dummyPassword = passwordEncoder.encode(generatePassword());

        Account account = Account.builder()
                .name("-")
                .profilePicture(generateAvatar(req.getName()))
                .password(dummyPassword)
                .phone(PhoneUtils.checkPhoneNumber(req.getPhone()))
                .role(AccountRoleEnum.USER)
                .status(EmployeeStatusEnum.WAITING_PHONE_VERIFICATION)
                .build();
        try {
            String accountId = getUUID();
            account.setId(accountId);
            EntityUtils.created(account, accountId);
            accountRepository.save(account);
            sendOtpEmployeeRegister(account, req.isSendWa());
            return ResponseRegisterEmployee.builder()
                    .id(accountId)
                    .nip(account.getNip())
                    .phone(PhoneUtils.checkPhoneNumber(req.getPhone()))
                    .nip(account.getNip())
                    .profilePicture(account.getProfilePicture())
                    .status(account.getStatus())
                    .build();
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public ResponseRegisterEmployee addUserData(RequestEmployeeRegister req) {
        String accountId = accountService.getCurrentAccountId();
        Account account = accountRepository.findById(accountId).orElseThrow(() -> new BadRequestException(ResponseEnum.ACCOUNT_NOT_FOUND));
        if (account.getStatus() != EmployeeStatusEnum.WAITING_REGISTRATION_DATA) {
            throw new BadRequestException(ResponseEnum.STATUS_NOT_VALID);
        }
        account.setName(req.getName());
        account.setNip(req.getNip());
        account.setEmail(req.getEmail());
        account.setStatus(EmployeeStatusEnum.WAITING_FACE_REGISTRATION);
        try {
            EntityUtils.updated(account, accountId);
            accountRepository.save(account);
            return ResponseRegisterEmployee.builder()
                    .name(account.getName())
                    .id(account.getId())
                    .nip(account.getNip())
                    .phone(PhoneUtils.checkPhoneNumber(account.getPhone()))
                    .status(account.getStatus())
                    .build();
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public ResponseRegisterEmployee addWorkUnitData(RequestEmployeeRegister req) {
        WorkUnit workUnit = workUnitRepository.findById(req.getWorkUnitId()).orElseThrow(() -> new BadRequestException(ResponseEnum.WORK_UNIT_NOT_FOUND));
        String accountId = accountService.getCurrentAccountId();
        Account account = accountRepository.findById(accountId).orElseThrow(() -> new BadRequestException(ResponseEnum.ACCOUNT_NOT_FOUND));
        if (account.getStatus() != EmployeeStatusEnum.WAITING_WORK_UNIT_DATA) {
            throw new BadRequestException(ResponseEnum.STATUS_NOT_VALID);
        }
        try {

            account.setWorkUnit(workUnit);
            account.setAgency(workUnit.getAgency());
            account.setStatus(EmployeeStatusEnum.WAITING_ADDRESS_DATA);
            EntityUtils.updated(account, accountId);
            accountRepository.save(account);
            return ResponseRegisterEmployee.builder()
                    .name(account.getName())
                    .id(account.getId())
                    .status(account.getStatus())
                    .nip(account.getNip())
                    .phone(PhoneUtils.checkPhoneNumber(account.getPhone()))
                    .workUnitId(account.getWorkUnit().getId())
                    .workUnitName(account.getWorkUnit().getName())
                    .agencyId(account.getAgency().getId())
                    .agencyName(account.getAgency().getName())
                    .build();
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public ResponseRegisterEmployee addAddressData(RequestEmployeeRegister req) {
        SubDistrict subDistrict = subDistrictRepository.findById(req.getSubDistrictId()).orElseThrow(() -> new NotFoundException(ResponseEnum.SUB_DISTRICT_NOT_FOUND));
        String accountId = accountService.getCurrentAccountId();
        Account account = accountRepository.findById(accountId).orElseThrow(() -> new BadRequestException(ResponseEnum.ACCOUNT_NOT_FOUND));
        if (account.getStatus() != EmployeeStatusEnum.WAITING_ADDRESS_DATA) {
            throw new BadRequestException(ResponseEnum.STATUS_NOT_VALID);
        }
        account.setSubDistrict(subDistrict);
        account.setAddress(req.getAddress());
        account.setStatus(EmployeeStatusEnum.WAITING_OTHER_DATA);
        try {
            EntityUtils.updated(account, accountId);
            accountRepository.save(account);
            return ResponseRegisterEmployee.builder()
                    .name(account.getName())
                    .id(account.getId())
                    .status(account.getStatus())
                    .nip(account.getNip())
                    .phone(PhoneUtils.checkPhoneNumber(account.getPhone()))
                    .workUnitId(account.getWorkUnit().getId())
                    .workUnitName(account.getWorkUnit().getName())
                    .agencyId(account.getAgency().getId())
                    .agencyName(account.getAgency().getName())
                    .build();
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public ResponseRegisterEmployee addOtherData(RequestRegisterOtherData req) {
        String accountId = accountService.getCurrentAccountId();
        Account account = accountRepository.findById(accountId).orElseThrow(() -> new BadRequestException(ResponseEnum.ACCOUNT_NOT_FOUND));
        if (account.getStatus() != EmployeeStatusEnum.WAITING_OTHER_DATA) {
            throw new BadRequestException(ResponseEnum.STATUS_NOT_VALID);
        }
        City city = cityRepository.findById(req.getPlaceOfBirthCityId()).orElseThrow(() -> new NotFoundException(ResponseEnum.CITY_NOT_FOUND));
        account.setNik(req.getNik());
        account.setFrontDegree(req.getFrontDegree());
        account.setBackDegree(req.getBackDegree());
        account.setPlaceOfBirthCity(city);
        account.setDateOfBirth(req.getDateOfBirth());
        account.setGender(req.getGender());
        account.setStatus(EmployeeStatusEnum.PENDING);
        account.setReligion(req.getReligion());
        account.setMaritalStatus(req.getMaritalStatus());
        EntityUtils.updated(account, accountId);
        try {
            accountRepository.save(account);
            return ResponseRegisterEmployee.builder()
                    .name(account.getName())
                    .id(account.getId())
                    .status(account.getStatus())
                    .nip(account.getNip())
                    .phone(PhoneUtils.checkPhoneNumber(account.getPhone()))
                    .workUnitId(account.getWorkUnit().getId())
                    .workUnitName(account.getWorkUnit().getName())
                    .agencyId(account.getAgency().getId())
                    .agencyName(account.getAgency().getName())
                    .build();
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public ResponseSignIn signUpOtpUser(RequestEmployeeRegister req) {
        Account account = accountRepository.findByPhoneAndActiveIsTrue(PhoneUtils.checkPhoneNumber(req.getPhone()))
                .orElseThrow(() -> new BadRequestException(ResponseEnum.PHONE_NOT_FOUND));

        OtpAndToken otpRecord = otpAndTokenRepository.findByAccountAndType(account, OtpAndTokenTypeEnum.SIGN_UP_USER_OTP)
                .orElseThrow(() -> new BadRequestException(ResponseEnum.INVALID_OTP));

        if (!otpRecord.getData().equals(req.getOtp())) {
            throw new BadRequestException(ResponseEnum.INVALID_OTP);
        }

        if (System.currentTimeMillis() > otpRecord.getExpiredDate()) {
            throw new BadRequestException(ResponseEnum.OTP_EXPIRE);
        }


        otpAndTokenRepository.delete(otpRecord);

        try {
            return buildSignInResponse(account);

        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    private ResponseSignIn buildSign(Account account, String password) {
        Authentication authentication;
        try {
            authentication = authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(account.getId(), password));
            SecurityContextHolder.getContext().setAuthentication(authentication);
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            String jwt = jwtService.generateToken(userDetails);
            List<String> accountImages = accountService.getEmployeeImages(account.getId(), AccountImagesType.REGISTER_IMAGE);
            ResponseAccountData accountData = buildAccountData(account, accountImages);
            return ResponseSignIn.builder().accessToken(jwt)
                    .accountData(accountData)
                    .build();
        } catch (AuthenticationException e) {
            throw new BadRequestException(ResponseEnum.SIGN_IN_FAILED);
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    private ResponseSignIn buildSignInResponse(Account account) {
        try {
            Authentication authentication = new UsernamePasswordAuthenticationToken(
                    account.getId(),
                    null,
                    null
            );
            SecurityContextHolder.getContext().setAuthentication(authentication);
            String jwt = jwtService.generateToken(account.getId());
            if (account.getStatus() == EmployeeStatusEnum.WAITING_PHONE_VERIFICATION) {
                account.setStatus(EmployeeStatusEnum.WAITING_REGISTRATION_DATA);
                EntityUtils.updated(account, account.getId());
                if (account.getPassword() == null || account.getPassword().trim().isEmpty()) {
                    account.setPassword(passwordEncoder.encode(generatePassword()));
                }
                accountRepository.save(account);
                log.info("User {} ({}) has been activated", account.getName(), account.getPhone());
            }
            ResponseAccountData accountData = buildAccountData(account);

            return ResponseSignIn.builder()
                    .accessToken(jwt)
                    .accountData(accountData)
                    .build();
        } catch (Exception e) {
            log.error("Error building sign in response for user: {}", account.getId(), e);
            throw new SystemErrorException(e);
        }
    }

    private String buildOtpMessage(String userName, String otp) {
        return String.format(
                """
                        🔐 *KODE OTP NUCA LALE* 🔐
                        
                        Halo %s,
                        
                        Kode OTP untuk login ke sistem Nuca Lale:
                        
                        📱 *%s*
                        
                        ⏰ Kode berlaku selama 1 menit
                        ⚠️ Jangan bagikan kode ini kepada siapapun
                        
                        Jika Anda tidak meminta kode ini, abaikan pesan ini.
                        
                        Terima kasih! 🙏""",
                userName, otp
        );
    }


    private void sendOtpEmployeeRegister(Account account, boolean isSendWa) {
        String otp = generateOTP(4);
        OtpAndToken otpAndToken = OtpAndToken.builder()
                .data(otp)
                .expiredDate(generateExpiredTimeInMillis(1))
                .type(OtpAndTokenTypeEnum.SIGN_UP_USER_OTP)
                .account(account)
                .build();
        otpAndTokenRepository.save(otpAndToken);

        if (isSendWa) {
            String message = buildOtpMessage(account.getName(), otp);
            whatsAppService.sendMessage(WhatsAppMessageRequest.builder()
                    .target(account.getPhone())
                    .message(message)
                    .countryCode("62")
                    .build());
        }
    }
}
