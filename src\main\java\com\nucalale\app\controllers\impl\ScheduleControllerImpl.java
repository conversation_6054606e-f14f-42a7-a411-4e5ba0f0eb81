package com.nucalale.app.controllers.impl;

import com.nucalale.app.annotations.BaseControllerImpl;
import com.nucalale.app.controllers.ScheduleController;
import com.nucalale.app.dto.request.RequestCreateEditSchedule;
import com.nucalale.app.dto.request.RequestCreateShift;
import com.nucalale.app.dto.request.RequestCreateWorkUnit;
import com.nucalale.app.dto.response.BaseResponse;
import com.nucalale.app.services.ScheduleService;
import com.nucalale.app.utils.ResponseHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;

import java.util.List;

@BaseControllerImpl
@RequiredArgsConstructor
public class ScheduleControllerImpl implements ScheduleController {
    private final ScheduleService scheduleService;

    @Override
    public BaseResponse createWorkUnit(RequestCreateWorkUnit req) {
        return ResponseHelper.createBaseResponse(scheduleService.createWorkUnit(req));
    }

    @Override
    public BaseResponse getWorkUnit(Pageable pageable, String agencyId, String query) {
        return ResponseHelper.createBaseResponse(scheduleService.getWorkUnit(pageable, agencyId, query));
    }

    @Override
    public BaseResponse getWorkUnitByAgencyId(String agencyId) {
        return ResponseHelper.createBaseResponse(scheduleService.getWorkUnitByAgencyId(agencyId));
    }

    @Override
    public BaseResponse getWorkUnitDetail(String workUnitId) {
        return ResponseHelper.createBaseResponse(scheduleService.getWorkUnitDetail(workUnitId));
    }

    @Override
    public BaseResponse updateWorkUnit(String workUnitId, RequestCreateWorkUnit req) {
        return ResponseHelper.createBaseResponse(scheduleService.updateWorkUnit(workUnitId, req));
    }

    @Override
    public BaseResponse createEditSchedule(RequestCreateEditSchedule req) {

        return ResponseHelper.createBaseResponse(scheduleService.createEditSchedule(req));
    }

    @Override
    public BaseResponse createShift(RequestCreateShift req) {
        return ResponseHelper.createBaseResponse(scheduleService.createShift(req));
    }

    @Override
    public BaseResponse getShift(String id) {
        return ResponseHelper.createBaseResponse(scheduleService.getShift(id));
    }

    public BaseResponse getShiftsByWorkUnitId(String workUnitId) {
    return  ResponseHelper.createBaseResponse(scheduleService.getShiftByWorkUnitId(workUnitId));
    }
}
