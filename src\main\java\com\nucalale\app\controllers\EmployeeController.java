package com.nucalale.app.controllers;

import com.nucalale.app.annotations.BaseController;
import com.nucalale.app.dto.request.RequestAddEmployeeImages;
import com.nucalale.app.dto.request.RequestCreateEmployee;
import com.nucalale.app.dto.response.BaseResponse;
import com.nucalale.app.enums.EmployeeStatusEnum;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@BaseController("employee")
public interface EmployeeController {

    @PostMapping("v1")
    BaseResponse createEmployee(@RequestBody RequestCreateEmployee request);

    @GetMapping("v1")
    BaseResponse getEmployees(
            Pageable pageable,
            @RequestParam(value = "agency_id", required = false) String agencyId,
            @RequestParam(value = "status", required = false) EmployeeStatusEnum status,
            @RequestParam(value = "q", required = false) String query
    );

    @PatchMapping("v1/{accountId}/resend-credentials")
    BaseResponse resendCredentials(@PathVariable("accountId") String accountId);

    @GetMapping("v1/{id}")
    BaseResponse getEmployee(@PathVariable("id") String id);

    @PostMapping("v1/register-face/{id}")
    BaseResponse registerEmployeeFace(@PathVariable("id") String id, @RequestParam("images") List<MultipartFile> images);

    @PostMapping("v1/m/register-face")
    BaseResponse registerEmployeeFace(@RequestParam("images") List<MultipartFile> images);

    @PostMapping("v1/validate")
    BaseResponse validateEmployee(@RequestParam("image") MultipartFile image);

    @GetMapping("v1/work-unit/{id}")
    BaseResponse listEmployeeByWorkUnit(@PathVariable("id") String id);

    @PatchMapping("v1/activated-employee/{id}")
    BaseResponse activateEmployee(@PathVariable("id") String id);

    @PostMapping("v1/add-employee-images")
    BaseResponse addEmployeeImages(@RequestBody RequestAddEmployeeImages req);

}
