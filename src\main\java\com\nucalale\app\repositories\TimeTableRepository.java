package com.nucalale.app.repositories;

import com.nucalale.app.entities.Shift;
import com.nucalale.app.entities.TimeTable;
import com.nucalale.app.enums.TimeTableTypeEnum;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface TimeTableRepository extends JpaRepository<TimeTable, String> {
    @Query("SELECT COUNT(DISTINCT tt.day) FROM TimeTable tt WHERE tt.workUnit.id = :workUnitId")
    int getTotalDayByWorkUnitId(@Param("workUnitId") String workUnitId);

    List<TimeTable> findByWorkUnitId(String workUnitId);


    List<TimeTable> findByShiftIdAndTypeAndActiveIsTrue(String shiftId, TimeTableTypeEnum timeTableTypeEnum);

    List<TimeTable> findByWorkUnitIdAndDay(String workUnitId, int day);
}
