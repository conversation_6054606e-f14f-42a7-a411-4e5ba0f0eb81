package com.nucalale.app.services.impl;

import com.nucalale.app.dto.request.RequestAttendance;
import com.nucalale.app.dto.response.ResponseDetailTimeTable;
import com.nucalale.app.dto.response.ResponseUserTimeTable;
import com.nucalale.app.entities.Account;
import com.nucalale.app.entities.Attendance;
import com.nucalale.app.entities.LocationWorkUnitRadius;
import com.nucalale.app.entities.TimeTable;
import com.nucalale.app.enums.ResponseEnum;
import com.nucalale.app.exceptions.BadRequestException;
import com.nucalale.app.exceptions.NotFoundException;
import com.nucalale.app.exceptions.SystemErrorException;
import com.nucalale.app.repositories.AccountRepository;
import com.nucalale.app.repositories.AttendanceRepository;
import com.nucalale.app.repositories.LocationWorkUnitRadiusRepository;
import com.nucalale.app.repositories.TimeTableRepository;
import com.nucalale.app.services.AccountService;
import com.nucalale.app.services.AttendanceService;
import com.nucalale.app.utils.EntityUtils;
import com.nucalale.app.utils.UtilsHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class AttendanceServiceImpl implements AttendanceService {
    private final AccountService accountService;
    private final TimeTableRepository timeTableRepository;
    private final LocationWorkUnitRadiusRepository locationWorkUnitRadiusRepository;
    private final AttendanceRepository attendanceRepository;
    private final AccountRepository accountRepository;

    @Override
    public List<ResponseUserTimeTable> getUserTimeTable(LocalDate date) {
        int day = date.getDayOfWeek().getValue();
        String currentAccountId = accountService.getCurrentAccountId();
        Account account = accountService.getAccount(currentAccountId).orElseThrow();
        String workUnitId = account.getWorkUnit().getId();
        if (workUnitId == null) {
            throw new NotFoundException(ResponseEnum.WORK_UNIT_NOT_FOUND);
        } else if (workUnitId.isEmpty()) {
            throw new NotFoundException(ResponseEnum.WORK_UNIT_NOT_FOUND);
        }
        List<TimeTable> timeTable = timeTableRepository.findByWorkUnitIdAndDay(workUnitId, day);
        List<LocationWorkUnitRadius> locationWorkUnitRadiusList = locationWorkUnitRadiusRepository.findByWorkUnitIdAndActiveIsTrue(workUnitId);
        List<ResponseUserTimeTable.Location> locationList = locationWorkUnitRadiusList.stream().map(e -> ResponseUserTimeTable.Location.builder()
                .id(e.getId())
                .lat(e.getLatitude())
                .lng(e.getLongitude())
                .name(e.getName())
                .radius(e.getRadius())
                .build()).toList();
        try {
            return timeTable.stream().map(e -> ResponseUserTimeTable.builder()
                    .id(e.getId())
                    .name(e.getName())
                    .startTime(e.getStartTime())
                    .endTime(e.getEndTime())
                    .alreadyAttendance(alreadyExistAttendance(currentAccountId, e.getId(), date))
                    .description(e.getDescription())
                    .locations(locationList)
                    .build()).toList();
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public ResponseDetailTimeTable getDetailTimeTable(String id) {
        TimeTable timeTable = timeTableRepository.findById(id).orElseThrow(() -> new NotFoundException(ResponseEnum.TIME_TABLE_NOT_FOUND));
        List<LocationWorkUnitRadius> locationWorkUnitRadiusList = locationWorkUnitRadiusRepository.findByWorkUnitIdAndActiveIsTrue(timeTable.getWorkUnit().getId());
        List<ResponseDetailTimeTable.Location> locationList = locationWorkUnitRadiusList.stream().map(e -> ResponseDetailTimeTable.Location.builder()
                .id(e.getId())
                .lat(e.getLatitude())
                .lng(e.getLongitude())
                .name(e.getName())
                .radius(e.getRadius())
                .build()).toList();
        try {
            return ResponseDetailTimeTable.builder()
                    .id(timeTable.getId())
                    .name(timeTable.getName())
                    .startTime(timeTable.getStartTime())
                    .endTime(timeTable.getEndTime())
                    .description(timeTable.getDescription())
                    .locations(locationList)
                    .build();
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public ResponseEnum attendance(RequestAttendance req) {
        if (req.getImageUrl() == null || req.getImageUrl().isEmpty() || req.getImageUrl().isBlank()) {
            throw new BadRequestException(ResponseEnum.IMAGE_REQUIRED);
        }
        Account account = accountService.getAccount(req.getAccountId()).orElseThrow(() -> new NotFoundException(ResponseEnum.ACCOUNT_NOT_FOUND));
        TimeTable timeTable = timeTableRepository.findById(req.getTimeTableId()).orElseThrow(() -> new NotFoundException(ResponseEnum.TIME_TABLE_NOT_FOUND));
        LocationWorkUnitRadius locationWorkUnitRadius = locationWorkUnitRadiusRepository.findByIdAndActiveIsTrue(req.getLocationId()).orElseThrow(() -> new NotFoundException(ResponseEnum.LOCATION_NOT_FOUND));

        checkAlreadyAttendance(req);

        if (!timeTable.getWorkUnit().getId().equals(locationWorkUnitRadius.getWorkUnit().getId())) {
            throw new BadRequestException(ResponseEnum.NOT_VALID_AREA);
        }
        if (!account.getWorkUnit().getId().equals(locationWorkUnitRadius.getWorkUnit().getId())) {
            throw new BadRequestException(ResponseEnum.ACCOUNT_NOT_REGISTERED_IN_SCHEDULE);
        }
        Double[] center = {locationWorkUnitRadius.getLatitude(), locationWorkUnitRadius.getLongitude()};
        Double[] userCenter = {req.getLat(), req.getLng()};
        boolean checkRadius = UtilsHelper.checkValidRadius(center, userCenter, locationWorkUnitRadius.getRadius());
        if (!checkRadius) {
            throw new BadRequestException(ResponseEnum.NOT_VALID_AREA);
        }
        LocalTime userTime = req.getDateTime().toLocalTime();
        Long durationGap = UtilsHelper.parseDurationGap(timeTable.getStartTime().toLocalTime(), userTime);
        Attendance attendance = Attendance.builder()
                .workUnit(locationWorkUnitRadius.getWorkUnit())
                .timeTable(timeTable)
                .account(account)
                .locationWorkUnitRadius(locationWorkUnitRadius)
                .lat(userCenter[0])
                .imageUrl(req.getImageUrl())
                .lng(userCenter[1])
                .timeGap(durationGap)
                .build();
        EntityUtils.created(attendance, account.getId());
        attendanceRepository.save(attendance);
        try {
            account.setLatestAttendanceTimeTable(timeTable.getId());
            account.setAttendanceDateTime(req.getDateTime());
            EntityUtils.updated(account, account.getId());
            accountRepository.save(account);
            return ResponseEnum.SUCCESS;
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    private void checkAlreadyAttendance(RequestAttendance req) {
        LocalDate dateOnly = req.getDateTime().toLocalDate();
        LocalDateTime startOfDay = dateOnly.atStartOfDay();
        LocalDateTime endOfDay = dateOnly.atTime(LocalTime.MAX);

        Optional<Attendance> attendanceOpt = attendanceRepository
                .findByAccountIdAndTimeTableIdAndCreatedDateBetween(req.getAccountId(), req.getTimeTableId(), startOfDay, endOfDay);

        if (attendanceOpt.isPresent()) {
            throw new BadRequestException(ResponseEnum.ALREADY_ATTENDANCE);
        }
    }

    private boolean alreadyExistAttendance(String accountId, String timeTableId, LocalDate date) {
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(LocalTime.MAX);

        Optional<Attendance> attendanceOpt = attendanceRepository
                .findByAccountIdAndTimeTableIdAndCreatedDateBetween(accountId, timeTableId, startOfDay, endOfDay);
        return attendanceOpt.isPresent();
    }
}
