package com.nucalale.app.dto.response;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.nucalale.app.enums.AccountRoleEnum;
import com.nucalale.app.enums.EmployeeStatusEnum;
import com.nucalale.app.enums.GenderEnum;
import com.nucalale.app.enums.MaritalStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ResponseDetailEmployee {
    private String accountId;
    private String name;
    private String phone;
    private String email;
    private String nip;
    private String nik;
    private AccountRoleEnum role;
    private String governmentRank;
    private String frontDegree;
    private String backDegree;
    private GenderEnum gender;
    private Date dateOfBirth;
    private String religion;
    private MaritalStatusEnum maritalStatus;
    private String taxIdNumber;
    private String address;
    private String profilePicture;
    private List<String> registerImages;

    private String agencyId;
    private String agencyName;

    private EmployeeStatusEnum status;
    private String workUnitId;
    private String workUnitName;

    private String subDistrictName;
    private String cityName;
    private String provinceName;

    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;
}