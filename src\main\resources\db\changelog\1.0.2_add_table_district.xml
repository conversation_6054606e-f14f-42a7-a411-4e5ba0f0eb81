<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="1.0.2" author="rivo pelu">
        <createTable tableName="district">
            <column name="id" type="int">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="province_id" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="city_id" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addForeignKeyConstraint baseTableName="district" baseColumnNames="province_id" constraintName="district_province" referencedTableName="province"
                                 referencedColumnNames="id"/>
        <addForeignKeyConstraint baseTableName="district" baseColumnNames="city_id" constraintName="district_city" referencedTableName="city"
                                 referencedColumnNames="id"/>
    </changeSet>
</databaseChangeLog>