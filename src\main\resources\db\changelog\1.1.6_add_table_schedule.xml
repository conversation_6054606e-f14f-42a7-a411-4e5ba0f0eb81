<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="1.1.6" author="rivo pelu">
        <createTable tableName="schedule">
            <column name="id" type="VARCHAR(32)">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="date" type="date"/>
            <column name="account_id" type="varchar(36)"/>
            <column name="time_table_id" type="varchar(36)"/>
            <column name="work_unit_id" type="varchar(36)"/>
            <!-- base entity fields -->
            <column name="active" type="BOOLEAN"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="created_date" type="TIMESTAMP"/>
            <column name="deleted_by" type="VARCHAR(255)"/>
            <column name="deleted_date" type="TIMESTAMP"/>
            <column name="updated_by" type="VARCHAR(255)"/>
            <column name="updated_date" type="TIMESTAMP"/>
        </createTable>
        <addForeignKeyConstraint
                baseTableName="schedule"
                baseColumnNames="account_id"
                constraintName="account_schedule_fk" referencedTableName="account"
                referencedColumnNames="id"
        />
        <addForeignKeyConstraint
                baseTableName="schedule"
                baseColumnNames="time_table_id"
                constraintName="timetable_schedule_fk" referencedTableName="time_table"
                referencedColumnNames="id"
        />
        <addForeignKeyConstraint
                baseTableName="schedule"
                baseColumnNames="work_unit_id"
                constraintName="work_unit_schedule_fk" referencedTableName="work_unit"
                referencedColumnNames="id"
        />
    </changeSet>
</databaseChangeLog>