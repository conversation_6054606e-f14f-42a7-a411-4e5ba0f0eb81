package com.nucalale.app.dto.response;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.nucalale.app.enums.AccountRoleEnum;
import com.nucalale.app.enums.EmployeeStatusEnum;
import com.nucalale.app.enums.GenderEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class ResponseAccountData {
    private String id;
    private String email;
    private String phoneNumber;
    private AccountRoleEnum role;
    private String profilePicture;
    private String name;
    private EmployeeStatusEnum status;
    private String workUnitId;
    private String workUnitName;
    private String agencyName;
    private String agencyId;
    private String nip;
    private String nik;
    private String religion;
    private GenderEnum gender;
    private String dateOfBirth;
    private String governmentRank;
    private String frontDegree;
    private String backDegree;
    private String taxIdNumber;
    private String address;
    private String subDistrictName;
    private String cityName;
    private String provinceName;
    private List<String> imageUrls;
}
