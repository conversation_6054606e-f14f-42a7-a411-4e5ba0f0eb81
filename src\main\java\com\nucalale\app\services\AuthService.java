package com.nucalale.app.services;

import com.nucalale.app.dto.request.RequestEmployeeRegister;
import com.nucalale.app.dto.request.RequestRegisterOtherData;
import com.nucalale.app.dto.request.RequestSignIn;
import com.nucalale.app.dto.request.RequestSignInUser;
import com.nucalale.app.dto.response.ResponseOtpAndToken;
import com.nucalale.app.dto.response.ResponseRegisterEmployee;
import com.nucalale.app.dto.response.ResponseSignIn;

public interface AuthService {

    ResponseSignIn signInAdmin(RequestSignIn req);

    ResponseOtpAndToken requestOtpSingInUser(RequestSignInUser requestSignIn);

    ResponseSignIn signInUserWithOtp(RequestSignInUser requestSignIn);

    ResponseRegisterEmployee signUpUser(RequestEmployeeRegister req);

    ResponseRegisterEmployee addUserData(RequestEmployeeRegister req);

    ResponseRegisterEmployee addWorkUnitData(RequestEmployeeRegister req);

    ResponseRegisterEmployee addAddressData(RequestEmployeeRegister req);

    ResponseRegisterEmployee addOtherData(RequestRegisterOtherData req);

    ResponseSignIn signUpOtpUser(RequestEmployeeRegister req);
}
