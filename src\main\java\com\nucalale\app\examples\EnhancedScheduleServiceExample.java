package com.nucalale.app.examples;

import com.nucalale.app.dto.request.RequestCreateEditSchedule;
import com.nucalale.app.dto.request.RequestCreateShift;
import com.nucalale.app.dto.request.RequestCreateWorkUnit;
import com.nucalale.app.enums.ResponseEnum;
import com.nucalale.app.observability.CustomMetrics;
import com.nucalale.app.observability.TracingUtils;
import com.nucalale.app.repositories.*;
import com.nucalale.app.services.AccountService;
import com.nucalale.app.services.ScheduleService;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.context.Scope;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * EXAMPLE: Enhanced ScheduleService with OpenTelemetry Custom Instrumentation
 * 
 * This is an example showing how to integrate custom OpenTelemetry instrumentation
 * into your existing ScheduleService. You can apply similar patterns to your actual
 * ScheduleServiceImpl.
 * 
 * Key features demonstrated:
 * - Custom spans for business operations
 * - Database operation tracing
 * - Custom metrics for business KPIs
 * - Error handling with proper observability
 * - Performance monitoring
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class EnhancedScheduleServiceExample implements ScheduleService {

    private final AgencyRepository agencyRepository;
    private final AccountService accountService;
    private final WorkUnitRepository workUnitRepository;
    private final TimeTableRepository timeTableRepository;
    private final LocationWorkUnitRadiusRepository locationWorkUnitRadiusRepository;
    private final ScheduleRepository scheduleRepository;
    private final AccountRepository accountRepository;
    private final ShiftRepository shiftRepository;
    
    // OpenTelemetry components
    private final TracingUtils tracingUtils;
    private final CustomMetrics customMetrics;

    @Override
    public ResponseEnum createWorkUnit(RequestCreateWorkUnit req) {
        long startTime = System.currentTimeMillis();
        
        // Create custom span for work unit creation
        Span span = tracingUtils.createWorkUnitOperationSpan("create", null, req.getAgencyId());
        
        try (Scope scope = span.makeCurrent()) {
            log.info("Creating work unit with request: {}", req);
            
            // Add custom attributes to span
            span.setAllAttributes(Attributes.of(
                    TracingUtils.OPERATION_TYPE, "work_unit_creation",
                    TracingUtils.ENTITY_TYPE, "work_unit",
                    TracingUtils.USER_ID, accountService.getCurrentAccountId()
            ));
            
            // Add event to track operation start
            tracingUtils.addEvent("work_unit_creation_started", Attributes.of(
                    TracingUtils.REQUEST_ID, req.toString()
            ));
            
            // Simulate database operations with custom spans
            ResponseEnum result = executeWorkUnitCreation(req);
            
            // Record success metrics
            customMetrics.recordWorkUnitCreation(req.getAgencyId());
            long duration = System.currentTimeMillis() - startTime;
            customMetrics.recordScheduleOperationDuration(duration, "create_work_unit");
            
            // Record success in span
            tracingUtils.recordSuccess("Work unit created successfully");
            
            log.info("Work unit created successfully for agency: {}", req.getAgencyId());
            return result;
            
        } catch (Exception e) {
            // Record error in observability
            tracingUtils.recordError(e, "work_unit_creation_error");
            log.error("Failed to create work unit for agency: {}", req.getAgencyId(), e);
            throw e;
        } finally {
            span.end();
        }
    }

    private ResponseEnum executeWorkUnitCreation(RequestCreateWorkUnit req) {
        // Example of database operation with custom span
        Span dbSpan = tracingUtils.createDatabaseOperationSpan("insert", "work_unit", 
                "INSERT INTO work_unit (name, agency_id) VALUES (?, ?)");
        
        try (Scope scope = dbSpan.makeCurrent()) {
            // Add database-specific attributes
            dbSpan.setAllAttributes(Attributes.of(
                    TracingUtils.ENTITY_ID, req.getAgencyId(),
                    TracingUtils.RECORD_COUNT, 1L
            ));
            
            // Simulate the actual database operation
            // Your actual implementation would go here
            // workUnitRepository.save(workUnit);
            
            tracingUtils.recordSuccess("Work unit inserted successfully");
            return ResponseEnum.SUCCESS;
            
        } catch (Exception e) {
            tracingUtils.recordError(e, "database_error");
            throw e;
        } finally {
            dbSpan.end();
        }
    }

    @Override
    public Object getWorkUnit(Pageable pageable, String agencyId, String query) {
        return tracingUtils.executeWithSpan(
                "schedule.get_work_units",
                Attributes.of(
                        TracingUtils.OPERATION_TYPE, "work_unit_query",
                        TracingUtils.ENTITY_TYPE, "work_unit",
                        TracingUtils.USER_ID, accountService.getCurrentAccountId()
                ),
                () -> {
                    long startTime = System.currentTimeMillis();
                    
                    // Add query parameters to current span
                    Span currentSpan = Span.current();
                    currentSpan.setAllAttributes(Attributes.of(
                            TracingUtils.ENTITY_ID, agencyId != null ? agencyId : "all",
                            TracingUtils.RECORD_COUNT, (long) pageable.getPageSize()
                    ));
                    
                    // Simulate database query
                    Object result = executeWorkUnitQuery(pageable, agencyId, query);
                    
                    // Record metrics
                    long duration = System.currentTimeMillis() - startTime;
                    customMetrics.recordScheduleOperationDuration(duration, "get_work_units");
                    
                    return result;
                }
        );
    }

    private Object executeWorkUnitQuery(Pageable pageable, String agencyId, String query) {
        // Create database query span
        Span dbSpan = tracingUtils.createDatabaseOperationSpan("select", "work_unit", 
                "SELECT * FROM work_unit WHERE agency_id = ? AND name LIKE ?");
        
        try (Scope scope = dbSpan.makeCurrent()) {
            // Your actual query implementation would go here
            // Page<WorkUnit> result = workUnitRepository.findByAgencyIdAndNameContaining(agencyId, query, pageable);
            
            // Simulate result
            Object result = "Mock work unit list";
            
            // Add result metadata to span
            dbSpan.setAllAttributes(Attributes.of(
                    TracingUtils.RECORD_COUNT, 10L // Mock count
            ));
            
            tracingUtils.recordSuccess("Work units retrieved successfully");
            return result;
            
        } finally {
            dbSpan.end();
        }
    }

    @Override
    public ResponseEnum createShift(RequestCreateShift req) {
        return tracingUtils.executeWithSpan(
                "schedule.create_shift",
                Attributes.of(
                        TracingUtils.OPERATION_TYPE, "shift_creation",
                        TracingUtils.ENTITY_TYPE, "shift",
                        TracingUtils.USER_ID, accountService.getCurrentAccountId()
                ),
                () -> {
                    long startTime = System.currentTimeMillis();
                    
                    // Record shift creation attempt
                    customMetrics.recordScheduleCreation("shift");
                    
                    // Your actual implementation would go here
                    ResponseEnum result = ResponseEnum.SUCCESS;
                    
                    // Record success metrics
                    long duration = System.currentTimeMillis() - startTime;
                    customMetrics.recordScheduleOperationDuration(duration, "create_shift");
                    
                    log.info("Shift created successfully");
                    return result;
                }
        );
    }

    @Override
    public Object getShift(Pageable pageable, String workUnitId, String query) {
        return tracingUtils.executeWithSpan(
                "schedule.get_shifts",
                Attributes.of(
                        TracingUtils.OPERATION_TYPE, "shift_query",
                        TracingUtils.ENTITY_TYPE, "shift",
                        TracingUtils.ENTITY_ID, workUnitId
                ),
                () -> {
                    // Your actual implementation would go here
                    return "Mock shift list";
                }
        );
    }

    @Override
    public ResponseEnum createSchedule(RequestCreateEditSchedule req) {
        long startTime = System.currentTimeMillis();
        
        Span span = tracingUtils.createScheduleOperationSpan("create", null);
        
        try (Scope scope = span.makeCurrent()) {
            // Add custom attributes
            span.setAllAttributes(Attributes.of(
                    TracingUtils.OPERATION_TYPE, "schedule_creation",
                    TracingUtils.USER_ID, accountService.getCurrentAccountId()
            ));
            
            // Record schedule creation
            customMetrics.recordScheduleCreation("regular");
            
            // Add event for operation start
            tracingUtils.addEvent("schedule_creation_started", Attributes.of(
                    TracingUtils.REQUEST_ID, req.toString()
            ));
            
            // Your actual implementation would go here
            ResponseEnum result = ResponseEnum.SUCCESS;
            
            // Record success
            long duration = System.currentTimeMillis() - startTime;
            customMetrics.recordScheduleOperationDuration(duration, "create_schedule");
            tracingUtils.recordSuccess("Schedule created successfully");
            
            return result;
            
        } catch (Exception e) {
            tracingUtils.recordError(e, "schedule_creation_error");
            throw e;
        } finally {
            span.end();
        }
    }

    @Override
    public Object getSchedule(Pageable pageable, String workUnitId, String query) {
        return tracingUtils.executeWithSpan(
                "schedule.get_schedules",
                Attributes.of(
                        TracingUtils.OPERATION_TYPE, "schedule_query",
                        TracingUtils.ENTITY_TYPE, "schedule",
                        TracingUtils.ENTITY_ID, workUnitId
                ),
                () -> {
                    // Your actual implementation would go here
                    return "Mock schedule list";
                }
        );
    }

    @Override
    public Object getSchedule(String id) {
        return tracingUtils.executeWithSpan(
                "schedule.get_schedule_by_id",
                Attributes.of(
                        TracingUtils.OPERATION_TYPE, "schedule_get",
                        TracingUtils.ENTITY_TYPE, "schedule",
                        TracingUtils.ENTITY_ID, id
                ),
                () -> {
                    // Your actual implementation would go here
                    return "Mock schedule details";
                }
        );
    }

    @Override
    public ResponseEnum updateSchedule(String id, RequestCreateEditSchedule req) {
        return tracingUtils.executeWithSpan(
                "schedule.update_schedule",
                Attributes.of(
                        TracingUtils.OPERATION_TYPE, "schedule_update",
                        TracingUtils.ENTITY_TYPE, "schedule",
                        TracingUtils.ENTITY_ID, id
                ),
                () -> {
                    long startTime = System.currentTimeMillis();
                    
                    // Your actual implementation would go here
                    ResponseEnum result = ResponseEnum.SUCCESS;
                    
                    // Record metrics
                    long duration = System.currentTimeMillis() - startTime;
                    customMetrics.recordScheduleOperationDuration(duration, "update_schedule");
                    
                    return result;
                }
        );
    }

    @Override
    public ResponseEnum deleteSchedule(String id) {
        return tracingUtils.executeWithSpan(
                "schedule.delete_schedule",
                Attributes.of(
                        TracingUtils.OPERATION_TYPE, "schedule_delete",
                        TracingUtils.ENTITY_TYPE, "schedule",
                        TracingUtils.ENTITY_ID, id
                ),
                () -> {
                    // Your actual implementation would go here
                    return ResponseEnum.SUCCESS;
                }
        );
    }
}
