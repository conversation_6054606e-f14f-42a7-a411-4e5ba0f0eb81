package com.nucalale.app.services.impl;

import com.nucalale.app.dto.request.RequestManageWorkUnitLocation;
import com.nucalale.app.dto.request.RequestWorkUnit;
import com.nucalale.app.dto.response.ResponseWorkUnit;
import com.nucalale.app.dto.response.ResponseWorkUnitLocation;
import com.nucalale.app.entities.Agency;
import com.nucalale.app.entities.LocationWorkUnitRadius;
import com.nucalale.app.entities.WorkUnit;
import com.nucalale.app.enums.ResponseEnum;
import com.nucalale.app.exceptions.NotFoundException;
import com.nucalale.app.exceptions.SystemErrorException;
import com.nucalale.app.repositories.AgencyRepository;
import com.nucalale.app.repositories.LocationWorkUnitRadiusRepository;
import com.nucalale.app.repositories.WorkUnitRepository;
import com.nucalale.app.services.AccountService;
import com.nucalale.app.services.WorkUnitService;
import com.nucalale.app.utils.EntityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class WorkUnitServiceImpl implements WorkUnitService {
    private final WorkUnitRepository workUnitRepository;
    private final AgencyRepository agencyRepository;
    private final AccountService accountService;
    private final LocationWorkUnitRadiusRepository locationWorkUnitRadiusRepository;

    @Override
    public ResponseWorkUnit createWorkUnit(RequestWorkUnit req) {
        String currentUserId = accountService.getCurrentAccountId();
        Agency agency = agencyRepository.findById(req.getAgencyId()).orElseThrow(() -> new RuntimeException("Agency not found"));
        try {
            WorkUnit workUnit = WorkUnit.builder()
                    .type(req.getType())
                    .agency(agency)
                    .name(req.getName())
                    .description(req.getDescription())
                    .build();
            EntityUtils.created(workUnit, currentUserId);
            workUnitRepository.save(workUnit);
            return buildResponseWorkUnit(workUnit);
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public ResponseWorkUnit editWorkUnit(RequestWorkUnit req, String id) {
        String currentAccountId = accountService.getCurrentAccountId();
        WorkUnit workUnit = workUnitRepository.findById(id).orElseThrow(() -> new NotFoundException(ResponseEnum.WORK_UNIT_NOT_FOUND));
        try {
            workUnit.setName(req.getName());
            workUnit.setDescription(req.getDescription());
            EntityUtils.updated(workUnit, currentAccountId);
            workUnitRepository.save(workUnit);
            return buildResponseWorkUnit(workUnit);
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public List<ResponseWorkUnitLocation> manageWorkUnitLocation(String id, List<RequestManageWorkUnitLocation> req) {
        String currentAccountId = accountService.getCurrentAccountId();
        WorkUnit workUnit = workUnitRepository.findById(id).orElseThrow(() -> new NotFoundException(ResponseEnum.WORK_UNIT_NOT_FOUND));
        List<LocationWorkUnitRadius> locationWorkUnitRadiusList = new ArrayList<>();
        for (RequestManageWorkUnitLocation location : req) {
            LocationWorkUnitRadius locationWorkUnitRadius;
            if (location.getId() == null) {
                locationWorkUnitRadius = null;
            } else {
                locationWorkUnitRadius = locationWorkUnitRadiusRepository.findById(location.getId()).orElse(null);
            }
            if (locationWorkUnitRadius == null) {
                locationWorkUnitRadius = LocationWorkUnitRadius.builder()
                        .name(location.getName())
                        .radius(location.getRadius())
                        .latitude(location.getLat())
                        .longitude(location.getLng())
                        .workUnit(workUnit)
                        .build();
                EntityUtils.created(locationWorkUnitRadius, currentAccountId);
            } else {
                locationWorkUnitRadius.setName(location.getName());
                locationWorkUnitRadius.setRadius(location.getRadius());
                locationWorkUnitRadius.setLatitude(location.getLat());
                locationWorkUnitRadius.setLongitude(location.getLng());
                EntityUtils.updated(locationWorkUnitRadius, currentAccountId);
            }
            locationWorkUnitRadiusList.add(locationWorkUnitRadius);
        }
        try {
            locationWorkUnitRadiusRepository.saveAll(locationWorkUnitRadiusList);
            return buildResponseWorkUnitLocationList(locationWorkUnitRadiusList);
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public List<ResponseWorkUnitLocation> getWorkUnitLocation(String id) {
        List<LocationWorkUnitRadius> locationWorkUnitRadiusList = locationWorkUnitRadiusRepository.findByWorkUnitIdAndActiveIsTrue(id);
        return  buildResponseWorkUnitLocationList(locationWorkUnitRadiusList);
    }

    private ResponseWorkUnit buildResponseWorkUnit(WorkUnit workUnit) {
        return ResponseWorkUnit.builder()
                .id(workUnit.getId())
                .description(workUnit.getDescription())
                .name(workUnit.getName())
                .type(workUnit.getType())
                .build();
    }

    private List<ResponseWorkUnitLocation> buildResponseWorkUnitLocationList(List<LocationWorkUnitRadius> locationWorkUnitRadiusList) {
        List<ResponseWorkUnitLocation> responseWorkUnitLocationList = new ArrayList<>();
        for (LocationWorkUnitRadius locationWorkUnitRadius : locationWorkUnitRadiusList) {
            ResponseWorkUnitLocation responseWorkUnitLocation = ResponseWorkUnitLocation.builder()
                    .id(locationWorkUnitRadius.getId())
                    .name(locationWorkUnitRadius.getName())
                    .radius(locationWorkUnitRadius.getRadius())
                    .lat(locationWorkUnitRadius.getLatitude())
                    .lng(locationWorkUnitRadius.getLongitude())
                    .build();
            responseWorkUnitLocationList.add(responseWorkUnitLocation);
        }
        return responseWorkUnitLocationList;
    }
}
