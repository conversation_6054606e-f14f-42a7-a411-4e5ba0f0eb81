package com.nucalale.app.entities;

import com.nucalale.app.enums.TimeTableTypeEnum;
import jakarta.persistence.*;
import lombok.*;

import java.sql.Time;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "time_table")
public class TimeTable extends BaseEntity {
    @Column(name = "day")
    private int day;

    @Column(name = "start_time")
    private Time startTime;

    @Column(name = "end_time")
    private Time endTime;

    @Column(name = "name")
    private String name;

    @Column(name = "seq")
    private Integer seq;

    @Column(name = "description")
    private String description;

    @ManyToOne
    @JoinColumn(name = "work_unit_id")
    private WorkUnit workUnit;

    @ManyToOne
    @JoinColumn(name = "shift_id")
    private Shift shift;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private TimeTableTypeEnum type;
}
