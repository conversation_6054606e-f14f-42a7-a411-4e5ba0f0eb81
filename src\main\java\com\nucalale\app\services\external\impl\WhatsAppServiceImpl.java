package com.nucalale.app.services.external.impl;

import com.nucalale.app.dto.external.WhatsAppMessageRequest;
import com.nucalale.app.dto.external.WhatsAppMessageResponse;
import com.nucalale.app.services.external.WhatsAppService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
@RequiredArgsConstructor
public class WhatsAppServiceImpl implements WhatsAppService {

    private final RestTemplate restTemplate;

    @Value("${whatsapp.fonnte.url:https://api.fonnte.com/send}")
    private String fonntteUrl;

    @Value("${whatsapp.fonnte.token}")
    private String fonntteToken;

    @Override
    public WhatsAppMessageResponse sendMessage(WhatsAppMessageRequest request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", fonntteToken);

            HttpEntity<WhatsAppMessageRequest> entity = new HttpEntity<>(request, headers);

            log.info("Sending WhatsApp message to: {}", request.getTarget());

            // Send request and get response as String
            ResponseEntity<String> response = restTemplate.exchange(
                    fonntteUrl,
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            String responseBody = response.getBody();
            log.info("WhatsApp API Response: {}", responseBody);

            // Check if request was successful based on HTTP status
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("WhatsApp message sent successfully to: {}", request.getTarget());
                return WhatsAppMessageResponse.builder()
                        .success(true)
                        .status("sent")
                        .message("Message sent successfully")
                        .target(request.getTarget())
                        .build();
            } else {
                log.error("WhatsApp API returned error status: {}", response.getStatusCode());
                return WhatsAppMessageResponse.builder()
                        .success(false)
                        .status("error")
                        .message("WhatsApp API returned error: " + response.getStatusCode())
                        .build();
            }

        } catch (Exception e) {
            log.error("Error sending WhatsApp message: {}", e.getMessage(), e);
            return WhatsAppMessageResponse.builder()
                    .success(false)
                    .status("error")
                    .message("Failed to send WhatsApp message")
                    .build();
        }
    }

    @Override
    public WhatsAppMessageResponse sendEmployeeCredentials(String phoneNumber, String employeeName, String nip, String temporaryPassword) {
        try {
            String message = buildEmployeeCredentialsMessage(employeeName, nip, temporaryPassword);
            String formattedPhone = formatPhoneNumber(phoneNumber);

            WhatsAppMessageRequest request = WhatsAppMessageRequest.builder()
                    .target(formattedPhone)
                    .message(message)
                    .countryCode("62")
                    .build();

            log.info("Sending employee credentials to: {} ({})", employeeName, formattedPhone);
            return sendMessage(request);

        } catch (Exception e) {
            log.error("Error preparing WhatsApp message for employee: {}", employeeName, e);
            return WhatsAppMessageResponse.builder()
                    .success(false)
                    .status("error")
                    .message("Failed to prepare WhatsApp message")
                    .build();
        }
    }

    private String buildEmployeeCredentialsMessage(String employeeName, String nip, String temporaryPassword) {
        return String.format(
                "🏢 *SELAMAT DATANG DI SISTEM NUCA LALE* 🏢\n\n" +
                        "Halo %s,\n\n" +
                        "Akun Anda telah berhasil dibuat untuk sistem absensi Nuca Lale.\n\n" +
                        "📋 *Detail Akun:*\n" +
                        "• NIP: %s\n" +
                        "• Password Sementara: %s\n\n" +
                        "🔐 *Cara Login:*\n" +
                        "1. Buka aplikasi Nuca Lale\n" +
                        "2. Masukkan NIP dan password sementara\n" +
                        "3. Ganti password setelah login pertama\n\n" +
                        "⚠️ *PENTING:*\n" +
                        "- Segera ganti password setelah login\n" +
                        "- Jangan bagikan password kepada orang lain\n" +
                        "- Simpan kredensial ini dengan aman\n\n" +
                        "Jika ada kendala, hubungi admin sistem.\n\n" +
                        "Terima kasih! 🙏",
                employeeName, nip, temporaryPassword
        );
    }

    private String formatPhoneNumber(String phoneNumber) {
        try {
            // Remove any non-digit characters
            String cleaned = phoneNumber.replaceAll("[^0-9]", "");

            // Validate phone number length
            if (cleaned.length() < 10 || cleaned.length() > 15) {
                log.warn("Invalid phone number length: {}", phoneNumber);
                return phoneNumber; // Return original if invalid
            }

            // Convert to international format
            if (cleaned.startsWith("0")) {
                return "62" + cleaned.substring(1);
            } else if (cleaned.startsWith("62")) {
                return cleaned;
            } else {
                return "62" + cleaned;
            }
        } catch (Exception e) {
            log.error("Error formatting phone number: {}", phoneNumber, e);
            return phoneNumber; // Return original if error
        }
    }
}
