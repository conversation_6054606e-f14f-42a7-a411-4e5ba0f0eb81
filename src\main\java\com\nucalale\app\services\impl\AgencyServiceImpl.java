package com.nucalale.app.services.impl;

import com.nucalale.app.dto.request.RequestCreateAgency;
import com.nucalale.app.dto.request.RequestUpdateAgency;
import com.nucalale.app.dto.response.ResponseDetailAgency;
import com.nucalale.app.dto.response.ResponseListAgency;
import com.nucalale.app.entities.Agency;
import com.nucalale.app.enums.ResponseEnum;
import com.nucalale.app.exceptions.NotFoundException;
import com.nucalale.app.repositories.AgencyRepository;
import com.nucalale.app.services.AccountService;
import com.nucalale.app.services.AgencyService;
import com.nucalale.app.utils.EntityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class AgencyServiceImpl implements AgencyService {
    private final AccountService accountService;
    private final AgencyRepository agencyRepository;

    @Override
    public ResponseEnum createAgency(RequestCreateAgency req) {
        String currentAccountId = accountService.getCurrentAccountId();
        Agency agency = Agency.builder()
                .name(req.getName())
                .lat(req.getLat())
                .lng(req.getLng())
                .logoUrl(req.getLogoUrl())
                .description(req.getDescription())
                .build();
        EntityUtils.created(agency, currentAccountId);
        agencyRepository.save(agency);
        return ResponseEnum.SUCCESS;
    }

    @Override
    public List<ResponseListAgency> listAgency() {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdDate");
        List<Agency> agencies = agencyRepository.findAll(sort);
        List<ResponseListAgency> responseListAgencies = new ArrayList<>();
        agencies.forEach(agency -> {
            ResponseListAgency responseListAgency = ResponseListAgency.builder()
                    .id(agency.getId())
                    .name(agency.getName())
                    .description(agency.getDescription())
                    .lat(agency.getLat())
                    .lng(agency.getLng())
                    .logoUrl(agency.getLogoUrl())
                    .createdDate(agency.getCreatedDate())
                    .build();
            responseListAgencies.add(responseListAgency);
        });
        return responseListAgencies;
    }

    @Override
    public ResponseDetailAgency getAgency(String id) {
        Agency agency = agencyRepository.findById(id).orElseThrow(() -> new NotFoundException(ResponseEnum.AGENCY_NOT_FOUND));
        return ResponseDetailAgency.builder()
                .id(agency.getId())
                .name(agency.getName())
                .description(agency.getDescription())
                .lat(agency.getLat())
                .lng(agency.getLng())
                .logoUrl(agency.getLogoUrl())
                .createdDate(agency.getCreatedDate())
                .build();
    }

    @Override
    public ResponseEnum updateAgency(String id, RequestUpdateAgency req) {
        String currentAccountId = accountService.getCurrentAccountId();

        Agency existingAgency = agencyRepository.findById(id)
                .orElseThrow(() -> new NotFoundException(ResponseEnum.AGENCY_NOT_FOUND));

        existingAgency.setName(req.getName());
        existingAgency.setDescription(req.getDescription());
        existingAgency.setLat(req.getLat());
        existingAgency.setLng(req.getLng());
        existingAgency.setLogoUrl(req.getLogoUrl());

        EntityUtils.updated(existingAgency, currentAccountId);

        agencyRepository.save(existingAgency);

        return ResponseEnum.SUCCESS;
    }
}
