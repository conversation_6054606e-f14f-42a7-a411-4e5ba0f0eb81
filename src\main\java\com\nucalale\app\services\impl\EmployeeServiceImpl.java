package com.nucalale.app.services.impl;

import com.nucalale.app.configs.MultipartInputStreamFileResource;
import com.nucalale.app.dto.external.WhatsAppMessageResponse;
import com.nucalale.app.dto.request.RequestAddEmployeeImages;
import com.nucalale.app.dto.request.RequestCreateEmployee;
import com.nucalale.app.dto.response.ResponseDetailEmployee;
import com.nucalale.app.dto.response.ResponseEmployeeCreated;
import com.nucalale.app.dto.response.ResponseListEmployee;
import com.nucalale.app.entities.*;
import com.nucalale.app.enums.AccountImagesType;
import com.nucalale.app.enums.EmployeeStatusEnum;
import com.nucalale.app.enums.ResponseEnum;
import com.nucalale.app.exceptions.BadRequestException;
import com.nucalale.app.exceptions.NotFoundException;
import com.nucalale.app.exceptions.SystemErrorException;
import com.nucalale.app.repositories.*;
import com.nucalale.app.services.AccountService;
import com.nucalale.app.services.EmployeeService;
import com.nucalale.app.services.external.WhatsAppService;
import com.nucalale.app.utils.EntityUtils;
import com.nucalale.app.utils.UtilsHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmployeeServiceImpl implements EmployeeService {

    private final AccountRepository accountRepository;
    private final AgencyRepository agencyRepository;
    private final SubDistrictRepository subDistrictRepository;
    private final AccountService accountService;
    private final PasswordEncoder passwordEncoder;
    private final WhatsAppService whatsAppService;
    private final WorkUnitRepository workUnitRepository;
    private final AccountImagesRepository accountImagesRepository;

    @Value("${url.face-recognition.service}")
    private String URL_FIRST_RECOGNITION_SERVICE;

    @Override
    @Transactional
    public ResponseEmployeeCreated createEmployee(RequestCreateEmployee request) {
        String currentAccountId = accountService.getCurrentAccountId();
        WorkUnit workUnit = workUnitRepository.findById(request.getWorkUnitId()).orElseThrow(() -> new NotFoundException(ResponseEnum.WORK_UNIT_NOT_FOUND));
        Agency agency = agencyRepository.findById(request.getAgencyId()).orElseThrow(() -> new NotFoundException(ResponseEnum.AGENCY_NOT_FOUND));

        Optional<Account> existingAccount = accountRepository.findByNipAndActiveIsTrue(request.getNip());
        if (existingAccount.isPresent()) {
            throw new BadRequestException(ResponseEnum.NIP_ALREADY_EXISTS);
        }

        if (request.getEmail() != null && !request.getEmail().trim().isEmpty()) {
            Optional<Account> existingEmailAccount = accountRepository.findByEmailAndActiveIsTrue(request.getEmail());
            if (existingEmailAccount.isPresent()) {
                throw new BadRequestException(ResponseEnum.EMAIL_ALREADY_EXIST);
            }
        }


        String temporaryPassword = UtilsHelper.generatePassword();
        String encodedPassword = passwordEncoder.encode(temporaryPassword);

        SubDistrict subDistrict = null;

        if (request.getSubDistrictId() != null) {
            subDistrict = subDistrictRepository.findById(request.getSubDistrictId()).orElse(null);
        }

        Account account = Account.builder()
                .status(EmployeeStatusEnum.WAITING_PHONE_VERIFICATION)
                .name(request.getName()).phone(request.getPhone()).email(request.getEmail()).password(encodedPassword).nip(request.getNip()).nik(request.getNik()).role(request.getRole()).governmentRank(request.getGovernmentRank()).frontDegree(request.getFrontDegree()).backDegree(request.getBackDegree()).workUnit(workUnit).religion(request.getReligion()).gender(request.getGender()).maritalStatus(request.getMaritalStatus()).dateOfBirth(request.getDateOfBirth()).taxIdNumber(request.getTaxIdNumber()).address(request.getAddress()).subDistrict(subDistrict).agency(agency).profilePicture(UtilsHelper.generateAvatar(request.getName())).build();

        EntityUtils.created(account, currentAccountId);

        Account savedAccount = accountRepository.save(account);


        boolean whatsAppSent = false;
        String whatsAppMessage = "WhatsApp not sent";

        try {
            log.info("Attempting to send WhatsApp credentials to: {} ({})", request.getName(), request.getPhone());
            if (request.isSendWa()) {

                WhatsAppMessageResponse whatsAppResponse = whatsAppService.sendEmployeeCredentials(request.getPhone(), request.getName(), request.getNip(), temporaryPassword);

                if (whatsAppResponse != null) {
                    whatsAppSent = Boolean.TRUE.equals(whatsAppResponse.getSuccess());
                    whatsAppMessage = whatsAppResponse.getMessage() != null ? whatsAppResponse.getMessage() : "WhatsApp response received";

                    log.info("WhatsApp response for {}: Success={}, Message={}", request.getName(), whatsAppSent, whatsAppMessage);
                } else {
                    whatsAppMessage = "No response from WhatsApp service";
                    log.warn("Null response from WhatsApp service for employee: {}", request.getName());
                }
            }

        } catch (Exception e) {
            log.error("Exception while sending WhatsApp to employee: {}", request.getName(), e);
            whatsAppMessage = e.getMessage() != null ? e.getMessage() : "Unknown error";
        }

        return ResponseEmployeeCreated.builder().accountId(savedAccount.getId()).name(savedAccount.getName()).phone(savedAccount.getPhone()).email(savedAccount.getEmail()).nip(savedAccount.getNip()).role(savedAccount.getRole()).agencyName(agency.getName()).whatsappSent(whatsAppSent).whatsappMessage(whatsAppMessage).createdDate(savedAccount.getCreatedDate()).build();
    }

    @Override
    public Page<ResponseListEmployee> getEmployees(Pageable pageable, String agencyId, String query, EmployeeStatusEnum status) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdDate");
        PageRequest pageRequest = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), sort);
        Page<Account> employees;

        employees = accountRepository.findAllEmployees(query, agencyId, status, pageRequest);

        List<ResponseListEmployee> responseList = mapToResponseListEmployee(employees.getContent());

        return new PageImpl<>(responseList, pageable, employees.getTotalElements());
    }

    @Override
    public ResponseEnum resendCredentials(String accountId) {
        Account account = accountRepository.findById(accountId).orElseThrow(() -> new NotFoundException(ResponseEnum.ACCOUNT_NOT_FOUND));

        String newTemporaryPassword = UtilsHelper.generatePassword();
        String encodedPassword = passwordEncoder.encode(newTemporaryPassword);

        account.setPassword(encodedPassword);
        EntityUtils.updated(account, accountService.getCurrentAccountId());
        accountRepository.save(account);

        try {
            WhatsAppMessageResponse response = whatsAppService.sendEmployeeCredentials(account.getPhone(), account.getName(), account.getNip(), newTemporaryPassword);

            if (response != null && Boolean.TRUE.equals(response.getSuccess())) {
                log.info("Credentials resent successfully to: {}", account.getName());
                return ResponseEnum.SUCCESS;
            } else {
                log.error("Failed to resend credentials via WhatsApp to: {}", account.getName());
                return ResponseEnum.WHATSAPP_SEND_FAILED;
            }

        } catch (Exception e) {
            log.error("Error resending credentials to: {}", account.getName(), e);
            return ResponseEnum.WHATSAPP_SEND_FAILED;
        }
    }

    @Override
    public ResponseDetailEmployee getEmployee(String id) {
        Account account = accountRepository.findById(id)
                .orElseThrow(() -> new NotFoundException(ResponseEnum.ACCOUNT_NOT_FOUND));
        try {
            Agency agency = account.getAgency();
            SubDistrict subDistrict = account.getSubDistrict();

            String subDistrictName = null;
            String cityName = null;
            String provinceName = null;

            if (subDistrict != null) {
                subDistrictName = subDistrict.getName();
                if (subDistrict.getCity() != null) {
                    cityName = subDistrict.getCity().getName();
                    if (subDistrict.getCity().getProvince() != null) {
                        provinceName = subDistrict.getCity().getProvince().getName();
                    }
                }
            }

            List<AccountImages> accountImagesList = accountImagesRepository.findByAccountIdAndType(account.getId(), AccountImagesType.REGISTER_IMAGE);
            List<String> imagesUrls = accountImagesList.stream().map(AccountImages::getUrl).toList();
            return ResponseDetailEmployee.builder()
                    .accountId(account.getId())
                    .name(account.getName())
                    .phone(account.getPhone())
                    .email(account.getEmail())
                    .registerImages(imagesUrls)
                    .nip(account.getNip())
                    .nik(account.getNik())
                    .role(account.getRole())
                    .governmentRank(account.getGovernmentRank())
                    .frontDegree(account.getFrontDegree())
                    .backDegree(account.getBackDegree())
                    .gender(account.getGender())
                    .dateOfBirth(account.getDateOfBirth())
                    .religion(account.getReligion())
                    .maritalStatus(account.getMaritalStatus())
                    .taxIdNumber(account.getTaxIdNumber())
                    .address(account.getAddress())
                    .profilePicture(account.getProfilePicture())
                    .agencyId(agency != null ? agency.getId() : null)
                    .agencyName(agency != null ? agency.getName() : null)
                    .workUnitId(account.getWorkUnit() != null ? account.getWorkUnit().getId() : null)
                    .workUnitName(account.getWorkUnit() != null ? account.getWorkUnit().getName() : null)
                    .status(account.getStatus())
                    .subDistrictName(subDistrictName)
                    .cityName(cityName)
                    .provinceName(provinceName)
                    .createdDate(account.getCreatedDate())
                    .updatedDate(account.getUpdatedDate())
                    .build();
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public ResponseEnum registerEmployeeFace(String id, List<MultipartFile> images) {
        Account account = accountService.getAccount(id).orElseThrow(() -> new NotFoundException(ResponseEnum.ACCOUNT_NOT_FOUND));
        try {
            String url = URL_FIRST_RECOGNITION_SERVICE + "/v1/register-face";

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("id", account.getId());
            body.add("name", account.getName());

            for (MultipartFile image : images) {
                body.add("images", new MultipartInputStreamFileResource(image.getInputStream(), image.getOriginalFilename(), image.getSize()));
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            RestTemplate restTemplate = new RestTemplate();
            restTemplate.postForEntity(url, requestEntity, Map.class);


            account.setStatus(EmployeeStatusEnum.WAITING_WORK_UNIT_DATA);
            EntityUtils.updated(account, account.getId());
            accountRepository.save(account);
            return ResponseEnum.SUCCESS;

        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public ResponseDetailEmployee validateEmployee(MultipartFile image) {
        try {
            String url = URL_FIRST_RECOGNITION_SERVICE + "/v1/validate-user";

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("image", new MultipartInputStreamFileResource(image.getInputStream(), image.getOriginalFilename(), image.getSize()));

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map> response = restTemplate.postForEntity(url, requestEntity, Map.class);

            Map responseBody = response.getBody();

            if (responseBody == null || !responseBody.containsKey("user_id")) {
                throw new BadRequestException(ResponseEnum.NOT_VALID_USER_ID);
            }

            String userId = responseBody.get("user_id").toString();
            return getEmployee(userId);

        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public List<ResponseListEmployee> listEmployeeByWorkUnit(String id) {
        WorkUnit workUnit = workUnitRepository.findByIdAndActiveIsTrue(id).orElseThrow(() -> new NotFoundException(ResponseEnum.WORK_UNIT_NOT_FOUND));
        List<Account> accounts = accountRepository.findByWorkUnitIdAndActiveIsTrue(workUnit.getId());
        try {
            return mapToResponseListEmployee(accounts);
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public ResponseEnum activateEmployee(String id) {
        String currentAccountId = accountService.getCurrentAccountId();
        Account account = accountRepository.findByIdAndActiveIsTrue(id).orElseThrow(() -> new NotFoundException(ResponseEnum.ACCOUNT_NOT_FOUND));
        try {
            account.setStatus(EmployeeStatusEnum.ACTIVE);
            EntityUtils.updated(account, currentAccountId);
            accountRepository.save(account);
            return ResponseEnum.SUCCESS;
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public List<String> addEmployeeImages(RequestAddEmployeeImages req) {
        String currentAccountId = accountService.getCurrentAccountId();
        Account account = accountRepository.findById(req.getAccountId()).orElseThrow(() -> new NotFoundException(ResponseEnum.ACCOUNT_NOT_FOUND));
        List<AccountImages> accountImagesList = new ArrayList<>();
        req.getUrls().forEach(url -> {
            AccountImages accountImages = AccountImages.builder()
                    .url(url)
                    .type(req.getType())
                    .account(account)
                    .build();
            EntityUtils.created(accountImages, currentAccountId);
            accountImagesList.add(accountImages);
        });
        try {
            accountImagesRepository.saveAll(accountImagesList);
            return accountImagesList.stream().map(AccountImages::getUrl).toList();
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }


    private List<ResponseListEmployee> mapToResponseListEmployee(List<Account> accounts) {
        List<ResponseListEmployee> responseList = new ArrayList<>();

        for (Account employee : accounts) {
            ResponseListEmployee response = ResponseListEmployee.builder().accountId(employee.getId()).name(employee.getName()).phone(employee.getPhone()).email(employee.getEmail()).nip(employee.getNip()).nik(employee.getNik()).role(employee.getRole()).governmentRank(employee.getGovernmentRank()).gender(employee.getGender()).dateOfBirth(employee.getDateOfBirth()).profilePicture(employee.getProfilePicture()).createdDate(employee.getCreatedDate()).updatedDate(employee.getUpdatedDate()).createdBy(employee.getCreatedBy()).active(employee.getActive()).status(employee.getStatus()).build();

            if (employee.getWorkUnit() != null) {
                response.setWorkUnitId(employee.getWorkUnit().getId());
                response.setWorkUnitName(employee.getWorkUnit().getName());
            }

            if (employee.getAgency() != null) {
                response.setAgencyId(employee.getAgency().getId());
                response.setAgencyName(employee.getAgency().getName());
            }

            if (employee.getSubDistrict() != null) {
                response.setSubDistrictName(employee.getSubDistrict().getName());
                if (employee.getSubDistrict().getCity() != null) {
                    response.setCityName(employee.getSubDistrict().getCity().getName());
                    if (employee.getSubDistrict().getCity().getProvince() != null) {
                        response.setProvinceName(employee.getSubDistrict().getCity().getProvince().getName());
                    }
                }
            }

            responseList.add(response);
        }
        return responseList;
    }
}
