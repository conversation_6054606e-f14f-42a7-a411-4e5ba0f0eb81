name: Deploy to VPS using Docker Hub

on:
  push:
    branches:
      - main

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '21'

      - name: Login to Docker Hub
        run: echo "${{ secrets.DOCKER_HUB_PASSWORD }}" | docker login -u ${{ secrets.DOCKER_HUB_USERNAME }} --password-stdin

      - name: Build JAR
        run: |
          chmod +x mvnw
          ./mvnw clean package -DskipTests

      - name: Verify JAR File
        run: ls -lah target/

      - name: Build and Push Docker Image
        run: |
          docker build --platform=linux/amd64 -t rivopelu12/nucalale:latest .
          docker push rivopelu12/nucalale:latest

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest

    steps:
      - name: Deploy to VPS
        uses: appleboy/ssh-action@v0.1.6
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USER }}
          key: ${{ secrets.VPS_SSH_KEY }}
          script: |
            docker login -u ${{ secrets.DOCKER_HUB_USERNAME }} --password ${{ secrets.DOCKER_HUB_PASSWORD }}
            docker stop nucalale || true
            docker rm -f nucalale || true
            docker pull rivopelu12/nucalale:latest
            docker run -d --name nucalale \
              -p 9765:9765 \
              -e SPRING_PROFILE=default \
              rivopelu12/nucalale:latest
