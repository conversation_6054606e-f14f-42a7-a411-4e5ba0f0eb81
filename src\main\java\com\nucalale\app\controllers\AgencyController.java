package com.nucalale.app.controllers;

import com.nucalale.app.annotations.BaseController;
import com.nucalale.app.dto.request.RequestCreateAgency;
import com.nucalale.app.dto.request.RequestUpdateAgency;
import com.nucalale.app.dto.response.BaseResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@BaseController("agency")
public interface AgencyController {
    @PostMapping("v1")
    BaseResponse createAgency( @RequestBody RequestCreateAgency req);

    @GetMapping("v1")
    BaseResponse getAgency();

    @GetMapping("v1/{id}")
    BaseResponse getAgency(@PathVariable("id") String id);

    @PutMapping("v1/{id}")
    BaseResponse updateAgency(@PathVariable("id") String id, @Validated @RequestBody RequestUpdateAgency req);


}
