package com.nucalale.app.entities;

import jakarta.persistence.*;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "attendance")
public class Attendance extends BaseEntity {

    @JoinColumn(name = "work_unit_id")
    @ManyToOne
    private WorkUnit workUnit;

    @JoinColumn(name = "time_table_id")
    @ManyToOne
    private TimeTable timeTable;

    @JoinColumn(name = "account_id")
    @ManyToOne
    private Account account;

    @JoinColumn(name = "location_work_unit_radius_id")
    @ManyToOne
    private LocationWorkUnitRadius locationWorkUnitRadius;

    @Column(name = "lat")
    private Double lat;


    @Column(name = "lng")
    private Double lng;

    @Column(name = "time_gap")
    private Long timeGap;

    @Column(name = "image_url")
    private String imageUrl;
}
