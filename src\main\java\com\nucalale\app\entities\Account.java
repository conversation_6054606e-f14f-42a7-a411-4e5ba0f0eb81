package com.nucalale.app.entities;

import com.nucalale.app.enums.AccountRoleEnum;
import com.nucalale.app.enums.EmployeeStatusEnum;
import com.nucalale.app.enums.GenderEnum;
import com.nucalale.app.enums.MaritalStatusEnum;
import jakarta.persistence.*;
import lombok.*;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "account")
public class Account extends BaseEntity implements UserDetails {

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "phone")
    private String phone;

    @Enumerated(EnumType.STRING)
    @Column(name = "role", nullable = false)
    private AccountRoleEnum role;

    @Column(name = "profile_picture")
    private String profilePicture;

    @Column(name = "email")
    private String email;

    @Column(name = "password", nullable = false)
    private String password;

    @Column(name = "nip")
    private String nip;

    @Column(name = "nik")
    private String nik;

    @Column(name = "government_rank")
    private String governmentRank;

    @Column(name = "front_degree")
    private String frontDegree;

    @Column(name = "back_degree")
    private String backDegree;

    @ManyToOne
    @JoinColumn(name = "place_of_birth_city_id")
    private City placeOfBirthCity;

    @Column(name = "religion")
    private String religion;

    @Enumerated(EnumType.STRING)
    @Column(name = "gender")
    private GenderEnum gender;

    @Enumerated(EnumType.STRING)
    @Column(name = "marital_status")
    private MaritalStatusEnum maritalStatus;

    @Column(name = "date_of_birth")
    private Date dateOfBirth;

    @Column(name = "tax_id_number")
    private String taxIdNumber;

    @Column(name = "address")
    private String address;

    @ManyToOne
    @JoinColumn(name = "sub_district_id")
    private SubDistrict subDistrict;

    @ManyToOne
    @JoinColumn(name = "agency_id")
    private Agency agency;

    @ManyToOne
    @JoinColumn(name = "work_unit_id")
    private WorkUnit workUnit;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private EmployeeStatusEnum status;

    @Column(name = "latest_attendance_time_table_id")
    private String latestAttendanceTimeTable;

    @Column(name = "attendance_date_time")
    private LocalDateTime attendanceDateTime;

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return Collections.singleton(new SimpleGrantedAuthority(role.name()));
    }

    @Override
    public String getUsername() {
        return this.getId();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return Boolean.TRUE.equals(this.getActive());
    }
}