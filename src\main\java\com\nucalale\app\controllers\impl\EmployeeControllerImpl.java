package com.nucalale.app.controllers.impl;

import com.nucalale.app.annotations.BaseControllerImpl;
import com.nucalale.app.controllers.EmployeeController;
import com.nucalale.app.dto.request.RequestAddEmployeeImages;
import com.nucalale.app.dto.request.RequestCreateEmployee;
import com.nucalale.app.dto.response.BaseResponse;
import com.nucalale.app.enums.EmployeeStatusEnum;
import com.nucalale.app.services.AccountService;
import com.nucalale.app.services.EmployeeService;
import com.nucalale.app.utils.ResponseHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RequiredArgsConstructor
@BaseControllerImpl
public class EmployeeControllerImpl implements EmployeeController {

    private final EmployeeService employeeService;
    private final AccountService accountService;

    @Override
    public BaseResponse createEmployee(RequestCreateEmployee request) {
        return ResponseHelper.createBaseResponse(employeeService.createEmployee(request));
    }

    @Override
    public BaseResponse getEmployees(Pageable pageable, String agencyId, EmployeeStatusEnum status, String query) {
        return ResponseHelper.createBaseResponse(employeeService.getEmployees(pageable, agencyId, query, status));
    }

    @Override
    public BaseResponse resendCredentials(String accountId) {
        return ResponseHelper.createBaseResponse(employeeService.resendCredentials(accountId));
    }

    @Override
    public BaseResponse getEmployee(String id) {
        return ResponseHelper.createBaseResponse(employeeService.getEmployee(id));
    }

    @Override
    public BaseResponse registerEmployeeFace(String id, List<MultipartFile> images) {
        return ResponseHelper.createBaseResponse(employeeService.registerEmployeeFace(id, images));
    }

    @Override
    public BaseResponse registerEmployeeFace(List<MultipartFile> images) {
        String currentAccountId = accountService.getCurrentAccountId();
        return ResponseHelper.createBaseResponse(employeeService.registerEmployeeFace(currentAccountId, images));
    }

    @Override
    public BaseResponse validateEmployee(MultipartFile image) {
        return ResponseHelper.createBaseResponse(employeeService.validateEmployee(image));
    }

    @Override
    public BaseResponse listEmployeeByWorkUnit(String id) {

        return ResponseHelper.createBaseResponse(employeeService.listEmployeeByWorkUnit(id));
    }

    @Override
    public BaseResponse activateEmployee(String id) {
        return ResponseHelper.createBaseResponse(employeeService.activateEmployee(id));
    }

    @Override
    public BaseResponse addEmployeeImages(RequestAddEmployeeImages req) {
        return ResponseHelper.createBaseResponse(employeeService.addEmployeeImages(req));
    }
}
