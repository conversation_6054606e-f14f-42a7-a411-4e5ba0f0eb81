package com.nucalale.app.services.impl;

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.Collections;

public class OtpAuthenticationToken extends AbstractAuthenticationToken {
    
    private final String accountId;
    private final String otp;

    public OtpAuthenticationToken(String accountId, String otp) {
        super(Collections.emptyList());
        this.accountId = accountId;
        this.otp = otp;
        setAuthenticated(false);
    }

    public OtpAuthenticationToken(String accountId, String otp, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.accountId = accountId;
        this.otp = otp;
        setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return otp;
    }

    @Override
    public Object getPrincipal() {
        return accountId;
    }
}