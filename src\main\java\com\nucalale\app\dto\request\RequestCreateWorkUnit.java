package com.nucalale.app.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.nucalale.app.enums.TimeTableTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Time;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class RequestCreateWorkUnit {
    private String name;
    private String description;
    private String agencyId;
    private TimeTableTypeEnum type;
    private List<TimetableData> timetable;
    private List<LocationRadius> location;
    private List<ShiftData> shifts;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonSerialize
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public static class LocationRadius {
        private Double lat;
        private Double lng;
        private Integer radius;
        private String name;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonSerialize
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public static class TimetableData {
        private int day;
        private String name;
        private String description;
        private Time startTime;
        private Time endTime;
    }



    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonSerialize
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public static class ShiftData {
        private String name;
        private Time startTime;
        private Time endTime;
        private List<ShiftTimeTable> timeTables;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonSerialize
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public static class ShiftTimeTable {
        private String name;
        private Time startTime;
        private Time endTime;
    }

}
