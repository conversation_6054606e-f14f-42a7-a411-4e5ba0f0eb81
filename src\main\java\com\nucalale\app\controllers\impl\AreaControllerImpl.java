package com.nucalale.app.controllers.impl;

import com.nucalale.app.annotations.BaseControllerImpl;
import com.nucalale.app.controllers.AreaController;
import com.nucalale.app.dto.response.BaseResponse;
import com.nucalale.app.services.AreaService;
import com.nucalale.app.utils.ResponseHelper;

import java.math.BigInteger;

@BaseControllerImpl
public class AreaControllerImpl implements AreaController {
    private final AreaService areaService;

    public AreaControllerImpl(AreaService areaService) {
        this.areaService = areaService;
    }

    @Override
    public BaseResponse getProvince() {
        return ResponseHelper.createBaseResponse(areaService.listProvince());
    }

    @Override
    public BaseResponse getCity(BigInteger id) {
        return ResponseHelper.createBaseResponse(areaService.listCity(id));
    }

    @Override
    public BaseResponse getDistrict(BigInteger id) {
        return ResponseHelper.createBaseResponse(areaService.listDistrict(id));
    }

    @Override
    public BaseResponse getSubDistrict(BigInteger id) {
        return ResponseHelper.createBaseResponse(areaService.listSubDistrict(id));
    }

    @Override
    public BaseResponse listCityBounce(String query) {
        return ResponseHelper.createBaseResponse(areaService.listCityBounce(query));
    }
}
