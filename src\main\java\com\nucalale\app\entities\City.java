package com.nucalale.app.entities;


import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigInteger;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "city")
public class City {
    @Id
    private BigInteger id;

    @JoinColumn(name = "province_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private Province province;

    @Column(name = "name")
    private String name;
}


