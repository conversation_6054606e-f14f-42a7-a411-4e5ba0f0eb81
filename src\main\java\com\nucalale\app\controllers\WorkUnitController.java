package com.nucalale.app.controllers;

import com.nucalale.app.annotations.BaseController;
import com.nucalale.app.dto.request.RequestManageWorkUnitLocation;
import com.nucalale.app.dto.request.RequestWorkUnit;
import com.nucalale.app.dto.response.BaseResponse;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@BaseController("work-unit")
public interface WorkUnitController {
    @PostMapping("v1")
    BaseResponse createWorkUnit(@RequestBody RequestWorkUnit req);

    @PutMapping("v1/{id}")
    BaseResponse editWorkUnit(@RequestBody RequestWorkUnit req, @PathVariable("id") String id);

    @PostMapping("v1/location/{id}")
    BaseResponse manageWorkUnitLocation(@PathVariable String id, @RequestBody List<RequestManageWorkUnitLocation> req);

    @GetMapping("v1/location/{id}")
    BaseResponse getWorkUnitLocation(@PathVariable String id);
}
