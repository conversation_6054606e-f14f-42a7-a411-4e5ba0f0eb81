package com.nucalale.app.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.nucalale.app.enums.AccountRoleEnum;
import com.nucalale.app.enums.EmployeeStatusEnum;
import com.nucalale.app.enums.GenderEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class ResponseListEmployee {
    private String accountId;
    private String name;
    private String phone;
    private String email;
    private String nip;
    private String nik;
    private AccountRoleEnum role;
    private String governmentRank;
    private GenderEnum gender;
    private Date dateOfBirth;
    private String profilePicture;

    // Agency info
    private String agencyId;
    private String agencyName;

    // SubDistrict info
    private String subDistrictName;
    private String cityName;
    private String provinceName;

    // Audit info
    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;
    private String createdBy;
    private Boolean active;
    private EmployeeStatusEnum status;
    private String workUnitId;
    private String workUnitName;

}
