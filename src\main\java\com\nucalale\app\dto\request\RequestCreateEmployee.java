package com.nucalale.app.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.nucalale.app.enums.AccountRoleEnum;
import com.nucalale.app.enums.GenderEnum;
import com.nucalale.app.enums.MaritalStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;
import java.util.Date;
import java.math.BigInteger;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class RequestCreateEmployee {
    
    @NotBlank(message = "Name is required")
    private String name;
    
    @NotBlank(message = "Phone number is required")
    @Pattern(regexp = "^(\\+62|62|0)[0-9]{9,13}$", message = "Invalid Indonesian phone number format")
    private String phone;
    
    @Email(message = "Invalid email format")
    private String email;
    
    @NotBlank(message = "NIP is required")
    private String nip;
    
    private String nik;
    
    @NotNull(message = "Role is required")
    private AccountRoleEnum role;
    
    private String governmentRank;
    private String frontDegree;
    private String backDegree;
    private BigInteger placeOfBirth;
    private String religion;
    
    private GenderEnum gender;
    
    private MaritalStatusEnum maritalStatus;
    private Date dateOfBirth;
    private String taxIdNumber;
    private String address;
    private BigInteger subDistrictId;
    
    private String agencyId;
    
    private Date startDate;
    private Date endDate;

    private String workUnitId;
    private boolean sendWa;
}
