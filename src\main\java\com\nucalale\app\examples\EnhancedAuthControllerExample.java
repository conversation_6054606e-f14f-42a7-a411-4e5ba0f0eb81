package com.nucalale.app.examples;

import com.nucalale.app.annotations.BaseControllerImpl;
import com.nucalale.app.controllers.AuthController;
import com.nucalale.app.dto.request.RequestEmployeeRegister;
import com.nucalale.app.dto.request.RequestRegisterOtherData;
import com.nucalale.app.dto.request.RequestSignIn;
import com.nucalale.app.dto.request.RequestSignInUser;
import com.nucalale.app.dto.response.BaseResponse;
import com.nucalale.app.observability.CustomMetrics;
import com.nucalale.app.observability.TracingUtils;
import com.nucalale.app.services.AuthService;
import com.nucalale.app.utils.ResponseHelper;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.context.Scope;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * EXAMPLE: Enhanced AuthController with OpenTelemetry Custom Instrumentation
 * 
 * This is an example showing how to integrate custom OpenTelemetry instrumentation
 * into your existing AuthController. You can apply similar patterns to your actual
 * AuthControllerImpl.
 * 
 * Key features demonstrated:
 * - Custom spans for each authentication operation
 * - Custom metrics for tracking authentication events
 * - Error handling with proper span status
 * - Adding custom attributes for better observability
 * - Performance timing measurements
 */
@BaseControllerImpl
@RequiredArgsConstructor
@Slf4j
public class EnhancedAuthControllerExample implements AuthController {
    
    private final AuthService authService;
    private final TracingUtils tracingUtils;
    private final CustomMetrics customMetrics;

    @Override
    public BaseResponse signInAdmin(RequestSignIn requestSignIn) {
        long startTime = System.currentTimeMillis();
        
        // Create custom span for admin sign-in
        Span span = tracingUtils.createAuthenticationSpan("admin_signin", 
                requestSignIn.getData(), "admin");
        
        try (Scope scope = span.makeCurrent()) {
            // Add custom attributes
            span.setAllAttributes(Attributes.of(
                    TracingUtils.OPERATION_TYPE, "admin_signin",
                    TracingUtils.USER_TYPE, "admin"
            ));
            
            // Record the attempt
            customMetrics.recordLoginAttempt("admin", "password");
            
            // Add event to span
            tracingUtils.addEvent("admin_signin_started", Attributes.of(
                    TracingUtils.USER_ID, requestSignIn.getData()
            ));
            
            // Execute the actual authentication
            BaseResponse response = ResponseHelper.createBaseResponse(
                    authService.signInAdmin(requestSignIn)
            );
            
            // Record success metrics
            customMetrics.recordLoginSuccess("admin", "password");
            long duration = System.currentTimeMillis() - startTime;
            customMetrics.recordAuthenticationDuration(duration, "admin_signin");
            
            // Record success in span
            tracingUtils.recordSuccess("Admin sign-in successful");
            
            log.info("Admin sign-in successful for user: {}", requestSignIn.getData());
            return response;
            
        } catch (Exception e) {
            // Record failure metrics
            customMetrics.recordLoginFailure("admin", "password", e.getClass().getSimpleName());
            
            // Record error in span
            tracingUtils.recordError(e, "authentication_error");
            
            log.error("Admin sign-in failed for user: {}", requestSignIn.getData(), e);
            throw e;
        } finally {
            span.end();
        }
    }

    @Override
    public BaseResponse requestOtpSingInUser(RequestSignInUser requestSignIn) {
        long startTime = System.currentTimeMillis();
        
        // Create custom span for OTP request
        Span span = tracingUtils.createOtpOperationSpan("request", 
                requestSignIn.getPhone(), "signin");
        
        try (Scope scope = span.makeCurrent()) {
            // Add custom attributes
            span.setAllAttributes(Attributes.of(
                    TracingUtils.OPERATION_TYPE, "otp_request",
                    TracingUtils.USER_TYPE, "user",
                    TracingUtils.USER_ID, requestSignIn.getPhone()
            ));
            
            // Record OTP generation attempt
            customMetrics.recordOtpGeneration("signin");
            
            // Execute the OTP request
            BaseResponse response = ResponseHelper.createBaseResponse(
                    authService.requestOtpSingInUser(requestSignIn)
            );
            
            // Record success
            long duration = System.currentTimeMillis() - startTime;
            customMetrics.recordAuthenticationDuration(duration, "otp_request");
            tracingUtils.recordSuccess("OTP request successful");
            
            log.info("OTP requested successfully for user: {}", requestSignIn.getPhone());
            return response;
            
        } catch (Exception e) {
            tracingUtils.recordError(e, "otp_request_error");
            log.error("OTP request failed for user: {}", requestSignIn.getPhone(), e);
            throw e;
        } finally {
            span.end();
        }
    }

    @Override
    public BaseResponse signInUserWithOtp(RequestSignInUser requestSignIn) {
        long startTime = System.currentTimeMillis();
        
        // Create custom span for OTP validation
        Span span = tracingUtils.createOtpOperationSpan("validate", 
                requestSignIn.getPhone(), "signin");
        
        try (Scope scope = span.makeCurrent()) {
            // Add custom attributes
            span.setAllAttributes(Attributes.of(
                    TracingUtils.OPERATION_TYPE, "otp_validation",
                    TracingUtils.USER_TYPE, "user",
                    TracingUtils.USER_ID, requestSignIn.getPhone()
            ));
            
            // Record validation attempt
            customMetrics.recordOtpValidation("attempt");
            customMetrics.recordLoginAttempt("user", "otp");
            
            // Execute the OTP validation
            BaseResponse response = ResponseHelper.createBaseResponse(
                    authService.signInUserWithOtp(requestSignIn)
            );
            
            // Record success metrics
            customMetrics.recordOtpValidation("success");
            customMetrics.recordOtpValidationSuccess();
            customMetrics.recordLoginSuccess("user", "otp");
            
            long duration = System.currentTimeMillis() - startTime;
            customMetrics.recordAuthenticationDuration(duration, "otp_validation");
            
            tracingUtils.recordSuccess("OTP validation successful");
            
            log.info("User sign-in with OTP successful for: {}", requestSignIn.getPhone());
            return response;
            
        } catch (Exception e) {
            // Record failure metrics
            customMetrics.recordOtpValidation("failure");
            customMetrics.recordLoginFailure("user", "otp", e.getClass().getSimpleName());
            
            tracingUtils.recordError(e, "otp_validation_error");
            log.error("User sign-in with OTP failed for: {}", requestSignIn.getPhone(), e);
            throw e;
        } finally {
            span.end();
        }
    }

    @Override
    public BaseResponse signUpUser(RequestEmployeeRegister req) {
        long startTime = System.currentTimeMillis();
        
        // Create custom span for user registration
        Span span = tracingUtils.createEmployeeOperationSpan("register", req.getPhone());
        
        try (Scope scope = span.makeCurrent()) {
            // Add custom attributes
            span.setAllAttributes(Attributes.of(
                    TracingUtils.OPERATION_TYPE, "user_registration",
                    TracingUtils.USER_TYPE, "user",
                    TracingUtils.USER_ID, req.getPhone()
            ));
            
            // Record registration attempt
            customMetrics.recordEmployeeRegistration("attempt");
            
            // Execute the registration
            BaseResponse response = ResponseHelper.createBaseResponse(
                    authService.signUpUser(req)
            );
            
            // Record success
            customMetrics.recordEmployeeRegistration("success");
            long duration = System.currentTimeMillis() - startTime;
            customMetrics.recordEmployeeOperationDuration(duration, "registration");
            
            tracingUtils.recordSuccess("User registration successful");
            
            log.info("User registration successful for: {}", req.getPhone());
            return response;
            
        } catch (Exception e) {
            customMetrics.recordEmployeeRegistration("failure");
            tracingUtils.recordError(e, "registration_error");
            log.error("User registration failed for: {}", req.getPhone(), e);
            throw e;
        } finally {
            span.end();
        }
    }

    @Override
    public BaseResponse signUpWithOtpUser(RequestEmployeeRegister req) {
        return tracingUtils.executeWithSpan(
                "auth.signup_with_otp",
                Attributes.of(
                        TracingUtils.OPERATION_TYPE, "signup_otp_validation",
                        TracingUtils.USER_TYPE, "user",
                        TracingUtils.USER_ID, req.getPhone()
                ),
                () -> {
                    customMetrics.recordOtpValidation("signup_attempt");
                    BaseResponse response = ResponseHelper.createBaseResponse(
                            authService.signUpOtpUser(req)
                    );
                    customMetrics.recordOtpValidation("signup_success");
                    return response;
                }
        );
    }

    @Override
    public BaseResponse addUserData(RequestEmployeeRegister req) {
        return tracingUtils.executeWithSpan(
                "auth.add_user_data",
                Attributes.of(
                        TracingUtils.OPERATION_TYPE, "add_user_data",
                        TracingUtils.USER_TYPE, "user"
                ),
                () -> {
                    customMetrics.recordEmployeeUpdate("add_user_data");
                    return ResponseHelper.createBaseResponse(authService.addUserData(req));
                }
        );
    }

    @Override
    public BaseResponse addWorkUnitData(RequestEmployeeRegister req) {
        return tracingUtils.executeWithSpan(
                "auth.add_work_unit_data",
                Attributes.of(
                        TracingUtils.OPERATION_TYPE, "add_work_unit_data",
                        TracingUtils.USER_TYPE, "user"
                ),
                () -> {
                    customMetrics.recordEmployeeUpdate("add_work_unit_data");
                    return ResponseHelper.createBaseResponse(authService.addWorkUnitData(req));
                }
        );
    }

    @Override
    public BaseResponse addAddressData(RequestEmployeeRegister req) {
        return tracingUtils.executeWithSpan(
                "auth.add_address_data",
                Attributes.of(
                        TracingUtils.OPERATION_TYPE, "add_address_data",
                        TracingUtils.USER_TYPE, "user"
                ),
                () -> {
                    customMetrics.recordEmployeeUpdate("add_address_data");
                    return ResponseHelper.createBaseResponse(authService.addAddressData(req));
                }
        );
    }

    @Override
    public BaseResponse addOtherData(RequestRegisterOtherData req) {
        return tracingUtils.executeWithSpan(
                "auth.add_other_data",
                Attributes.of(
                        TracingUtils.OPERATION_TYPE, "add_other_data",
                        TracingUtils.USER_TYPE, "user"
                ),
                () -> {
                    customMetrics.recordEmployeeUpdate("add_other_data");
                    return ResponseHelper.createBaseResponse(authService.addOtherData(req));
                }
        );
    }
}
