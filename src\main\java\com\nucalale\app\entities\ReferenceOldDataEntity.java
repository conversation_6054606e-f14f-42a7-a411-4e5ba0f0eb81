package com.nucalale.app.entities;

import com.nucalale.app.enums.GenderEnum;
import com.nucalale.app.enums.MaritalStatusEnum;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Entity
@Table(name = "reference_old_data")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReferenceOldDataEntity {

    @Id
    private String id;

    @Column(name = "name", length = 256)
    private String name;

    @Column(name = "nip", length = 64)
    private String nip;

    @Column(name = "old_nip", length = 64)
    private String oldNip;

    @Column(name = "nik", length = 64)
    private String nik;

    @Column(name = "government_rank", length = 128)
    private String governmentRank;

    @Column(name = "front_degree", length = 128)
    private String frontDegree;

    @Column(name = "back_degree", length = 128)
    private String backDegree;

    @Column(name = "religion", length = 64)
    private String religion;

    @Column(name = "date_of_birth")
    private Date dateOfBirth;

    @Column(name = "tax_id_number", length = 64)
    private String taxIdNumber;

    @Enumerated(EnumType.STRING)
    @Column(name = "marital_status", length = 64)
    private MaritalStatusEnum maritalStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "gender", length = 64)
    private GenderEnum gender;

    @Column(name = "address")
    private String address;

    @Column(name = "email")
    private String email;

}
