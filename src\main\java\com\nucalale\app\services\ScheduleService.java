package com.nucalale.app.services;


import com.nucalale.app.dto.request.RequestCreateEditSchedule;
import com.nucalale.app.dto.request.RequestCreateShift;
import com.nucalale.app.dto.request.RequestCreateWorkUnit;
import com.nucalale.app.dto.response.ResponseDetailShift;
import com.nucalale.app.dto.response.ResponseDetailWorkUnit;
import com.nucalale.app.dto.response.ResponseListShift;
import com.nucalale.app.dto.response.ResponseListWorkUnit;
import com.nucalale.app.enums.ResponseEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ScheduleService {


    ResponseEnum createWorkUnit(RequestCreateWorkUnit req);

    List<ResponseListWorkUnit> getWorkUnitByAgencyId(String agencyId);

    ResponseDetailWorkUnit getWorkUnitDetail(String workUnitId);

    ResponseEnum updateWorkUnit(String workUnitId, RequestCreateWorkUnit req);

    Page<ResponseListWorkUnit> getWorkUnit(Pageable pageable, String agencyId, String query);

    ResponseEnum createEditSchedule(RequestCreateEditSchedule req);

    ResponseEnum createShift(RequestCreateShift req);

    ResponseDetailShift getShift(String id);

    List<ResponseListShift> getShiftByWorkUnitId(String workUnitId);
}
