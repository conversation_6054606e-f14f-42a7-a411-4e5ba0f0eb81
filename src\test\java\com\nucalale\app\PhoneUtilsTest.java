package com.nucalale.app;

import com.nucalale.app.utils.PhoneUtils;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class PhoneUtilsTest {

    @Test
    void testCheckPhoneNumber_WithZeroPrefix() {
        String input = "08123456789";
        String expected = "628123456789";
        assertEquals(expected, PhoneUtils.checkPhoneNumber(input));
    }

    @Test
    void testCheckPhoneNumber_With62Prefix() {
        String input = "628123456789";
        String expected = "628123456789";
        assertEquals(expected, PhoneUtils.checkPhoneNumber(input));
    }

    @Test
    void testCheckPhoneNumber_With620Prefix() {
        String input = "6208123456789";
        String expected = "628123456789";
        assertEquals(expected, PhoneUtils.checkPhoneNumber(input));
    }

    @Test
    void testCheckPhoneNumber_With8Prefix() {
        String input = "8123456789";
        String expected = "628123456789";
        assertEquals(expected, PhoneUtils.checkPhoneNumber(input));
    }

    @Test
    void testCheckPhoneNumber_WithPlusSign() {
        String input = "+628123456789";
        String expected = "628123456789";
        assertEquals(expected, PhoneUtils.checkPhoneNumber(input));
    }

    @Test
    void testCheckPhoneNumber_Empty() {
        String input = "";
        String expected = "";
        assertEquals(expected, PhoneUtils.checkPhoneNumber(input));
    }

    @Test
    void testCheckPhoneNumber_Null() {
        String input = null;
        String expected = "";
        assertEquals(expected, PhoneUtils.checkPhoneNumber(input));
    }

    @Test
    void testCheckPhoneNumber_WithSpecialChars() {
        String input = "(+62) 812-3456-789";
        String expected = "628123456789";
        assertEquals(expected, PhoneUtils.checkPhoneNumber(input));
    }
}
