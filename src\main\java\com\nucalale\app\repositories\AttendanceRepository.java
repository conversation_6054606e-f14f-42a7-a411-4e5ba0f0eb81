package com.nucalale.app.repositories;

import com.nucalale.app.entities.Attendance;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.Optional;

public interface AttendanceRepository extends JpaRepository<Attendance, String> {
    Optional<Attendance> findByAccountIdAndTimeTableIdAndCreatedDateBetween(
            String accountId,
            String timeTableId,
            LocalDateTime startDate,
            LocalDateTime endDate
    );}
