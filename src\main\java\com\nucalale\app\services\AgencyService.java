package com.nucalale.app.services;


import com.nucalale.app.dto.request.RequestCreateAgency;
import com.nucalale.app.dto.request.RequestUpdateAgency;
import com.nucalale.app.dto.response.ResponseDetailAgency;
import com.nucalale.app.dto.response.ResponseListAgency;
import com.nucalale.app.enums.ResponseEnum;

import java.util.List;

public interface AgencyService {
    ResponseEnum createAgency(RequestCreateAgency req);

    List<ResponseListAgency> listAgency();

    ResponseDetailAgency getAgency(String id);

    ResponseEnum updateAgency(String id, RequestUpdateAgency req);


}
