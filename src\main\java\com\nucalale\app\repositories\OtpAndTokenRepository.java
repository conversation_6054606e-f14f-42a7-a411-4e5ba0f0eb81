package com.nucalale.app.repositories;

import com.nucalale.app.entities.Account;
import com.nucalale.app.entities.OtpAndToken;
import com.nucalale.app.enums.OtpAndTokenTypeEnum;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface OtpAndTokenRepository extends JpaRepository<OtpAndToken, String> {
    Optional<OtpAndToken> findByAccountAndType(Account account, OtpAndTokenTypeEnum type);
}
