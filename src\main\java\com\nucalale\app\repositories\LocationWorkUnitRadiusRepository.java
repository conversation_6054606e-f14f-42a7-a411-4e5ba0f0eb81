package com.nucalale.app.repositories;

import com.nucalale.app.entities.LocationWorkUnitRadius;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface LocationWorkUnitRadiusRepository extends JpaRepository<LocationWorkUnitRadius, String> {
    int countLocationWorkUnitRadiusByWorkUnitId(String workUnitId);

    List<LocationWorkUnitRadius> findByWorkUnitIdAndActiveIsTrue(String workUnitId);

    Optional<LocationWorkUnitRadius> findByIdAndActiveIsTrue(String id);
}
