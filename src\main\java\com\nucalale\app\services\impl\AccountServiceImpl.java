package com.nucalale.app.services.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nucalale.app.constants.AuthConstant;
import com.nucalale.app.dto.response.ResponseAccountData;
import com.nucalale.app.entities.Account;
import com.nucalale.app.entities.AccountImages;
import com.nucalale.app.enums.AccountImagesType;
import com.nucalale.app.exceptions.SystemErrorException;
import com.nucalale.app.repositories.AccountImagesRepository;
import com.nucalale.app.repositories.AccountRepository;
import com.nucalale.app.services.AccountService;
import com.nucalale.app.utils.UtilsHelper;
import jakarta.servlet.ServletRequest;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class AccountServiceImpl implements AccountService {
    private final AccountRepository accountRepository;
    private final ServletRequest httpServletRequest;
    private final AccountImagesRepository accountImagesRepository;

    public AccountServiceImpl(AccountRepository accountRepository, ServletRequest httpServletRequest, ObjectMapper objectMapper, AccountImagesRepository accountImagesRepository) {
        this.accountRepository = accountRepository;
        this.httpServletRequest = httpServletRequest;
        this.accountImagesRepository = accountImagesRepository;
    }

    @Override
    public ResponseAccountData getMeAccount() {
        String userId = getCurrentAccountId();
        Account account = accountRepository.findByIdAndActiveIsTrue(userId).orElseThrow(() -> new RuntimeException("Account not found"));
        List<String> accountImagesList = getEmployeeImages(account.getId(), AccountImagesType.REGISTER_IMAGE);
        ResponseAccountData responseAccountData = UtilsHelper.buildAccountData(account, accountImagesList);
        try {
            return responseAccountData;
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public Optional<Account> getAccount(String id) {
        return accountRepository.findById(id);
    }


    @Override
    public ResponseAccountData getCurrentAccount() {
        try {
            ResponseAccountData res = (ResponseAccountData) httpServletRequest.getAttribute(AuthConstant.HEADER_X_USER);
            System.out.println(res);
            return res;
        } catch (Exception e) {
            throw new RuntimeException("Failed to extract current account from request", e);
        }
    }

    @Override
    public String getCurrentAccountId() {
        return httpServletRequest.getAttribute(AuthConstant.HEADER_X_WHO).toString();
    }

    @Override
    public List<String> getEmployeeImages(String accountId, AccountImagesType type) {
        List<AccountImages> accountImagesList = accountImagesRepository.findByAccountIdAndType(accountId, type);
        return accountImagesList.stream().map(AccountImages::getUrl).toList();
    }
}
