spring.application.name=<PERSON><PERSON>-<PERSON><PERSON>
#REDIST
spring.data.redis.host=*************
spring.data.redis.port=6379
spring.cache.type=redis
# DATABASE
spring.datasource.url=*********************************************************
spring.datasource.username=rivo
spring.datasource.password=@Rivopelu123
#Liquibase
spring.liquibase.enabled=false
spring.liquibase.change-log=classpath:/db/changelog/changelog-master.xml
# app
spring.jpa.hibernate.ddl-auto=none
server.servlet.context-path=/api
spring.data.web.pageable.default-page-size=10
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
spring.jpa.show-sql=false
# AUTH
auth.secret=404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
auth.expiration_time=********

# WHATSAPP FONNTE
whatsapp.fonnte.url=https://api.fonnte.com/send
whatsapp.fonnte.token=WnBPWrSWcZzqPJTkorZ2
server.port=9765

auth.first.account.name=admin
auth.first.account.email=<EMAIL>
auth.first.account.password=admin

url.face-recognition.service=http://*************:8000/api