package com.nucalale.app.controllers;

import com.nucalale.app.annotations.BaseController;
import com.nucalale.app.annotations.PublicAccess;
import com.nucalale.app.dto.request.RequestEmployeeRegister;
import com.nucalale.app.dto.request.RequestRegisterOtherData;
import com.nucalale.app.dto.request.RequestSignIn;
import com.nucalale.app.dto.request.RequestSignInUser;
import com.nucalale.app.dto.response.BaseResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@BaseController("auth")
public interface AuthController {

    @PublicAccess
    @PostMapping("v1/admin/sign-in")
    BaseResponse signInAdmin(@RequestBody RequestSignIn requestSignIn);

    @PublicAccess
    @PostMapping("v1/user/sign-in/request-otp")
    BaseResponse requestOtpSingInUser(@RequestBody RequestSignInUser requestSignIn);

    @PublicAccess
    @PostMapping("v1/user/sign-in/otp")
    BaseResponse signInUserWithOtp(@RequestBody RequestSignInUser requestSignIn);

    @PublicAccess
    @PostMapping("v1/user/sign-up")
    BaseResponse signUpUser(@RequestBody RequestEmployeeRegister req);


    @PublicAccess
    @PostMapping("v1/user/sign-up/otp")
    BaseResponse signUpWithOtpUser(@RequestBody RequestEmployeeRegister req);


    @PostMapping("v1/m/add-user-data")
    BaseResponse addUserData(@RequestBody RequestEmployeeRegister req);


    @PostMapping("v1/m/add-work-unit-data")
    BaseResponse addWorkUnitData(@RequestBody RequestEmployeeRegister req);

    @PostMapping("v1/m/add-address")
    BaseResponse addAddressData(@RequestBody RequestEmployeeRegister req);

    @PostMapping("v1/m/other-data")
    BaseResponse addOtherData(@RequestBody RequestRegisterOtherData req);

}
