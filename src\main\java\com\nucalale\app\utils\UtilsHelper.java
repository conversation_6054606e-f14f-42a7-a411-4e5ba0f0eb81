package com.nucalale.app.utils;

import com.nucalale.app.dto.response.ResponseAccountData;
import com.nucalale.app.entities.Account;
import com.nucalale.app.entities.WorkUnit;
import org.springframework.stereotype.Component;

import javax.print.Doc;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalTime;
import java.util.List;
import java.util.UUID;

@Component
public class UtilsHelper {

    private static final String UPPER = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String LOWER = "abcdefghijklmnopqrstuvwxyz";
    private static final String DIGITS = "**********";
    private static final SecureRandom random = new SecureRandom();
    private static final double EARTH_RADIUS = 6371000;

    private static final String ALL = UPPER + LOWER + DIGITS;
    private static final int PASSWORD_LENGTH = 8;

    public static ResponseAccountData buildAccountData(Account account, List<String> urls) {
        ResponseAccountData accountData = buildAccountData(account);
        accountData.setImageUrls(urls);
        return accountData;
    }

    public static ResponseAccountData buildAccountData(Account account) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        ResponseAccountData accountData = ResponseAccountData.builder()
                .id(account.getId())
                .email(account.getEmail())
                .phoneNumber(account.getPhone())
                .role(account.getRole())
                .profilePicture(account.getProfilePicture())
                .name(account.getName())
                .status(account.getStatus())
                .nip(account.getNip())
                .nik(account.getNik())
                .religion(account.getReligion())
                .gender(account.getGender())
                .dateOfBirth(account.getDateOfBirth() != null ? dateFormat.format(account.getDateOfBirth()) : null)
                .governmentRank(account.getGovernmentRank())
                .frontDegree(account.getFrontDegree())
                .backDegree(account.getBackDegree())
                .taxIdNumber(account.getTaxIdNumber())
                .address(account.getAddress())
                .build();

        if (account.getSubDistrict() != null) {
            accountData.setSubDistrictName(account.getSubDistrict().getName());
            if (account.getSubDistrict().getCity() != null) {
                accountData.setCityName(account.getSubDistrict().getCity().getName());
                if (account.getSubDistrict().getCity().getProvince() != null) {
                    accountData.setProvinceName(account.getSubDistrict().getCity().getProvince().getName());
                }
            }
        }

        if (account.getWorkUnit() != null) {
            accountData.setWorkUnitId(account.getWorkUnit().getId());
            accountData.setWorkUnitName(account.getWorkUnit().getName());
            if (account.getWorkUnit().getAgency() != null) {
                accountData.setAgencyName(account.getWorkUnit().getAgency().getName());
                accountData.setAgencyId(account.getWorkUnit().getAgency().getId());
            }
        } else if (account.getAgency() != null) {
            accountData.setAgencyName(account.getAgency().getName());
            accountData.setAgencyId(account.getAgency().getId());
        }

        return accountData;
    }


    public static String generateAvatar(String name) {
        return "https://ui-avatars.com/api/?name=" + name;
    }

    public static String generatePassword() {
        SecureRandom random = new SecureRandom();
        StringBuilder password = new StringBuilder(PASSWORD_LENGTH);

        for (int i = 0; i < PASSWORD_LENGTH; i++) {
            int index = random.nextInt(ALL.length());
            password.append(ALL.charAt(index));
        }

        return password.toString();
    }

    public static String generateUniqueSlug(String e) {
        String slug = e.replaceAll("[^a-zA-Z0-9\\s]", "")
                .replaceAll("\\s+", " ")
                .trim();
        slug = slug.toLowerCase();
        slug = slug.replaceAll("\\s", "-");
        return slug;
    }


    public static String generateOTP(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("Panjang OTP harus lebih dari 0");
        }

        StringBuilder otp = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            otp.append(DIGITS.charAt(random.nextInt(DIGITS.length())));
        }

        return otp.toString();
    }


    public static Long generateExpiredTimeInMillis(int minute) {
        return System.currentTimeMillis() + (minute * 60 * 1000L);
    }

    public static String getUUID() {
        return UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
    }

    public static Long parseDurationGap(LocalTime startTime, LocalTime userTime) {
        Duration gap = Duration.between(startTime, userTime);
        long gapInMinutes = gap.toMinutes();
        long gapInSeconds = gap.toSeconds();
        System.out.println("Gap in seconds: " + gapInSeconds);
        System.out.println("Gap in minutes: " + gapInMinutes);
        return gapInSeconds;
    }

    public static boolean checkValidRadius(Double[] systemLocation, Double[] userLocation, int radius) {
        double lat1 = Math.toRadians(systemLocation[0]);
        double lon1 = Math.toRadians(systemLocation[1]);
        double lat2 = Math.toRadians(userLocation[0]);
        double lon2 = Math.toRadians(userLocation[1]);

        double dLat = lat2 - lat1;
        double dLon = lon2 - lon1;

        double a = Math.pow(Math.sin(dLat / 2), 2)
                + Math.cos(lat1) * Math.cos(lat2)
                * Math.pow(Math.sin(dLon / 2), 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        double distance = EARTH_RADIUS * c;

        return distance <= radius;
    }

}
