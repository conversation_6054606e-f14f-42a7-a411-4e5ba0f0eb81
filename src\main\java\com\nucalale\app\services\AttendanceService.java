package com.nucalale.app.services;

import com.nucalale.app.dto.request.RequestAttendance;
import com.nucalale.app.dto.response.ResponseDetailTimeTable;
import com.nucalale.app.dto.response.ResponseUserTimeTable;
import com.nucalale.app.enums.ResponseEnum;

import java.time.LocalDate;
import java.util.List;

public interface AttendanceService {

    List<ResponseUserTimeTable> getUserTimeTable(LocalDate day);

    ResponseDetailTimeTable getDetailTimeTable(String id);

    ResponseEnum attendance(RequestAttendance req);
}
