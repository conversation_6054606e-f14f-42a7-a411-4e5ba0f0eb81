package com.nucalale.app.controllers;

import com.nucalale.app.annotations.BaseController;
import com.nucalale.app.dto.response.BaseResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigInteger;

@BaseController("area")
public interface AreaController {
    @GetMapping("province")
    BaseResponse getProvince();

    @GetMapping("city/{id}")
    BaseResponse getCity(@PathVariable("id") BigInteger id);

    @GetMapping("district/{id}")
    BaseResponse getDistrict(@PathVariable("id") BigInteger id);

    @GetMapping("sub-district/{id}")
    BaseResponse getSubDistrict(@PathVariable("id") BigInteger id);

    @GetMapping("list-city/bounce")
    BaseResponse listCityBounce(@RequestParam(value = "q") String query);
}
