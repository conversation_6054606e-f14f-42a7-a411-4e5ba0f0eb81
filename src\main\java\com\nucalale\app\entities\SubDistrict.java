package com.nucalale.app.entities;

import jakarta.persistence.*;
import lombok.*;

import java.math.BigInteger;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "sub_district")
public class SubDistrict {

    @Id
    private BigInteger id;

    @Column(name = "name", nullable = false)
    private String name;

    @ManyToOne
    @JoinColumn(name = "province_id", nullable = false)
    private Province province;

    @ManyToOne
    @JoinColumn(name = "city_id", nullable = false)
    private City city;

    @ManyToOne
    @JoinColumn(name = "district_id")
    private District district;

    @Column(name = "postcode")
    private BigInteger postalCode;
}