package com.nucalale.app.dto.request;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.nucalale.app.enums.GenderEnum;
import com.nucalale.app.enums.MaritalStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigInteger;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class RequestRegisterOtherData {
    private String nik;
    private String frontDegree;
    private String backDegree;
    private BigInteger placeOfBirthCityId;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateOfBirth;
    private GenderEnum gender;
    private String religion;
    private MaritalStatusEnum maritalStatus;
}
