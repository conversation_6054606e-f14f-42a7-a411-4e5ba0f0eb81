package com.nucalale.app.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.nucalale.app.enums.GenderEnum;
import com.nucalale.app.enums.MaritalStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ResponseOldData {

    private String id;
    private String name;


    private String nip;


    private String oldNip;


    private String nik;


    private String governmentRank;


    private String frontDegree;


    private String backDegree;


    private String religion;


    private Date dateOfBirth;


    private String taxIdNumber;

    private MaritalStatusEnum maritalStatus;


    private GenderEnum gender;
}
