package com.nucalale.app.services;

import com.nucalale.app.dto.request.RequestManageWorkUnitLocation;
import com.nucalale.app.dto.request.RequestWorkUnit;
import com.nucalale.app.dto.response.ResponseWorkUnit;
import com.nucalale.app.dto.response.ResponseWorkUnitLocation;

import java.util.List;

public interface WorkUnitService {
    ResponseWorkUnit createWorkUnit(RequestWorkUnit req);

    ResponseWorkUnit editWorkUnit(RequestWorkUnit req, String id);

    List<ResponseWorkUnitLocation> manageWorkUnitLocation(String id, List<RequestManageWorkUnitLocation> req);


    List<ResponseWorkUnitLocation> getWorkUnitLocation(String id);
}
