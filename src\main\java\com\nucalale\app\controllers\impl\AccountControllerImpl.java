package com.nucalale.app.controllers.impl;

import com.nucalale.app.annotations.BaseControllerImpl;
import com.nucalale.app.controllers.AccountController;
import com.nucalale.app.dto.response.BaseResponse;
import com.nucalale.app.services.AccountService;
import com.nucalale.app.utils.ResponseHelper;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@BaseControllerImpl
public class AccountControllerImpl implements AccountController {
    private final AccountService accountService;

    @Override
    public BaseResponse getMe() {
        return ResponseHelper.createBaseResponse(accountService.getMeAccount());
    }
}
