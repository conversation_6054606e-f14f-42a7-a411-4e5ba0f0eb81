package com.nucalale.app.repositories;

import com.nucalale.app.entities.Account;
import com.nucalale.app.enums.EmployeeStatusEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface AccountRepository extends JpaRepository<Account, String> {
    Optional<Account> findByIdAndActiveIsTrue(String id);

    Optional<Account> findByEmailAndActiveIsTrue(String email);

    Optional<Account> findByNipAndActiveIsTrue(String nip);

    @Query("""
                 SELECT a FROM Account a
                 WHERE a.active = true
                   AND (:agencyId is null or :agencyId = '' or a.agency.id = :agencyId )
                   AND (:status is null or :status = '' or a.status = :status )
                   AND (
                     :query IS NULL OR :query = ''
                     OR LOWER(a.name) LIKE LOWER(CONCAT('%', :query, '%'))
                     OR LOWER(a.nip) LIKE LOWER(CONCAT('%', :query, '%'))
                   )
            \s""")
    Page<Account> findAllEmployees(
            @Param("query") String query,
            @Param("agencyId") String agencyId,
            @Param("status") EmployeeStatusEnum status,
            Pageable pageable
    );


    List<Account> findByWorkUnitIdAndActiveIsTrue(String id);

    Optional<Account> findByPhoneAndActiveIsTrue(String phone);

}