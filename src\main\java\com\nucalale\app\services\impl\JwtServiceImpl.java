package com.nucalale.app.services.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nucalale.app.dto.response.ResponseAccountData;
import com.nucalale.app.entities.Account;
import com.nucalale.app.enums.ResponseEnum;
import com.nucalale.app.exceptions.NotFoundException;
import com.nucalale.app.exceptions.SystemErrorException;
import com.nucalale.app.repositories.AccountRepository;
import com.nucalale.app.services.AccountService;
import com.nucalale.app.services.JwtService;
import com.nucalale.app.utils.UtilsHelper;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import lombok.RequiredArgsConstructor;
import org.apache.coyote.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import static com.nucalale.app.constants.AuthConstant.*;


@Service
@RequiredArgsConstructor
public class JwtServiceImpl implements JwtService {
    @Value("${auth.secret}")
    private String SECRET;

    @Value("${auth.expiration_time}")
    private long EXPIRATION_TIME;

    private final AccountRepository accountRepository;

    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }


    public String generateToken(UserDetails userDetails) {

        Optional<Account> findAccount = accountRepository.findByIdAndActiveIsTrue(userDetails.getUsername());
        if (findAccount.isEmpty()) {
            throw new NotFoundException(ResponseEnum.ACCOUNT_NOT_FOUND);
        }
        Account account = findAccount.get();
        account.setPassword(null);
        ObjectMapper objectMapper = new ObjectMapper()
                .registerModule(new JavaTimeModule())
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        ResponseAccountData responseAccountData = UtilsHelper.buildAccountData(account);
        Map<String, Object> claims = new HashMap<>();
        try {
            String userJson = objectMapper.writeValueAsString(responseAccountData);
            claims.put(HEADER_X_USER, userJson);
            claims.put(HEADER_X_WHO, account.getId());
            claims.put(HEADER_X_ROLE, account.getRole().name());
            claims.put(HEADER_X_MAIL, account.getEmail());
            return generateToken(claims, userDetails);
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    public String generateToken(String userId) {
        Optional<Account> findAccount = accountRepository.findByIdAndActiveIsTrue(userId);
        if (findAccount.isEmpty()) {
            throw new NotFoundException(ResponseEnum.ACCOUNT_NOT_FOUND);
        }
        Account account = findAccount.get();
        account.setPassword(null);
        ObjectMapper objectMapper = new ObjectMapper()
                .registerModule(new JavaTimeModule())
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        ResponseAccountData responseAccountData = UtilsHelper.buildAccountData(account);
        Map<String, Object> claims = new HashMap<>();
        try {
            String userJson = objectMapper.writeValueAsString(responseAccountData);
            claims.put(HEADER_X_USER, userJson);
            claims.put(HEADER_X_WHO, account.getId());
            claims.put(HEADER_X_ROLE, account.getRole().name());
            claims.put(HEADER_X_MAIL, account.getEmail());
            return generateTokenWithClaims(claims, userId);
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    public String generateToken(Map<String, Object> extractClaims, UserDetails userDetails) {
        return Jwts.builder().setClaims(extractClaims).setSubject(userDetails.getUsername()).setExpiration(new Date(System.currentTimeMillis() + EXPIRATION_TIME)).signWith(getSignInKey(), SignatureAlgorithm.HS256).compact();
    }

    public String generateTokenWithClaims(Map<String, Object> extractClaims, String userId) {
        return Jwts.builder()
                .setClaims(extractClaims)
                .setSubject(userId)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + EXPIRATION_TIME))
                .signWith(getSignInKey(), SignatureAlgorithm.HS256)
                .compact();
    }


    public boolean isTokenValid(String token, UserDetails userDetails) {
        final String username = extractUsername(token);
        return (username.equals(userDetails.getUsername()));
    }


    private Key getSignInKey() {
        byte[] keyBytes = Decoders.BASE64.decode(SECRET);
        return Keys.hmacShaKeyFor(keyBytes);
    }

    private Claims extractAllClaims(String token) {
        return Jwts.parserBuilder().setSigningKey(getSignInKey()).build().parseClaimsJws(token).getBody();
    }
}


