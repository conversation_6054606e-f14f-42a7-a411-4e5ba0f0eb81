package com.nucalale.app.utils;
public class PhoneUtils {
    public static String checkPhoneNumber(String phone) {
        if (phone == null || phone.isEmpty()) return "";

        phone = phone.replaceAll("[^0-9]", "");

        if (phone.startsWith("6208")) {
            return "62" + phone.substring(3);
        }

        if (phone.startsWith("62")) {
            return phone;
        }

        if (phone.startsWith("0")) {
            return "62" + phone.substring(1);
        }

        if (phone.startsWith("8")) {
            return "62" + phone;
        }

        if (phone.startsWith("620")) {
            return "62" + phone.substring(3);
        }

        // Default fallback
        return phone;
    }
}