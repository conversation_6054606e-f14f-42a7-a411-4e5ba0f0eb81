<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="1.3.6" author="rivopelu">
        <createTable tableName="account_images">
            <column name="id" type="VARCHAR(32)">
                <constraints primaryKey="true" nullable="false"/>
            </column>

            <column name="url" type="varchar(600)"/>
            <column name="type" type="varchar(45)"/>
            <column name="account_id" type="varchar(40)"/>
            <!-- base entity fields -->
            <column name="active" type="BOOLEAN"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="created_date" type="TIMESTAMP"/>
            <column name="deleted_by" type="VARCHAR(255)"/>
            <column name="deleted_date" type="TIMESTAMP"/>
            <column name="updated_by" type="VARCHAR(255)"/>
            <column name="updated_date" type="TIMESTAMP"/>
        </createTable>

        <addForeignKeyConstraint
                baseTableName="account_images"
                baseColumnNames="account_id"
                constraintName="account_images_account_fk"
                referencedTableName="account"
                referencedColumnNames="id"
        />
    </changeSet>
</databaseChangeLog>