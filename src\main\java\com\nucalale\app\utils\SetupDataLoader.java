package com.nucalale.app.utils;

import com.nucalale.app.entities.Account;
import com.nucalale.app.enums.AccountRoleEnum;
import com.nucalale.app.repositories.AccountRepository;
import jakarta.transaction.Transactional;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class SetupDataLoader implements ApplicationListener<ContextRefreshedEvent> {

    private final PasswordEncoder passwordEncoder;
    private final AccountRepository accountRepository;
    boolean alreadySetup = false;

    @Value("${auth.first.account.name}")
    private String FIRST_ACCOUNT_NAME;

    @Value("${auth.first.account.email}")
    private String FIRST_ACCOUNT_EMAIL;

    @Value("${auth.first.account.password}")
    private String FIRST_ACCOUNT_PASSWORD;


    void createFirstAccount() {
        String id = UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
        String encodedPassword = passwordEncoder.encode(FIRST_ACCOUNT_PASSWORD);
        Account account = Account.builder()
                .name(FIRST_ACCOUNT_NAME)
                .email(FIRST_ACCOUNT_EMAIL)
                .password(encodedPassword)
                .role(AccountRoleEnum.SUPER_ADMIN)
                .subDistrict(null)
                .placeOfBirthCity(null)
                .profilePicture(UtilsHelper.generateAvatar(FIRST_ACCOUNT_NAME))
                .build();
        account.setId(id);
        EntityUtils.created(account, id);
        accountRepository.save(account);
    }

    private boolean isNotEmpty(String str) {
        return str != null && !str.trim().isEmpty();
    }

    @Override
    @Transactional
    public void onApplicationEvent(@NonNull ContextRefreshedEvent event) {
        Optional<Account> findAccount = accountRepository.findByEmailAndActiveIsTrue(FIRST_ACCOUNT_EMAIL);
        if (alreadySetup) return;
        if (findAccount.isEmpty() && isNotEmpty(FIRST_ACCOUNT_EMAIL) && isNotEmpty(FIRST_ACCOUNT_PASSWORD)) {
            createFirstAccount();
        }
        alreadySetup = true;
    }
}

