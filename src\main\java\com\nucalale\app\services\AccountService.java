package com.nucalale.app.services;

import com.nucalale.app.dto.response.ResponseAccountData;
import com.nucalale.app.entities.Account;
import com.nucalale.app.enums.AccountImagesType;

import java.util.List;
import java.util.Optional;

public interface AccountService {

    ResponseAccountData getMeAccount();

    Optional<Account> getAccount(String id);

    ResponseAccountData getCurrentAccount();

    String getCurrentAccountId();

    List<String> getEmployeeImages(String accountId, AccountImagesType type);

}