package com.nucalale.app.dto.response;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Time;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ResponseDetailShift {
    private String id;
    private String name;
    private String description;
    private String agencyId;
    private String agencyName;
    private String workUnitId;
    private String workUnitName;
    private Time startTime;
    private Time endTime;
    private List<ShiftTimeTable> timeTables;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonSerialize
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ShiftTimeTable {
        private String name;
        private Time startTime;
        private Time endTime;
    }

}
