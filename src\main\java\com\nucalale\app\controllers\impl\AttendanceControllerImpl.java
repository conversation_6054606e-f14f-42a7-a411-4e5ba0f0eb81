package com.nucalale.app.controllers.impl;

import com.nucalale.app.annotations.BaseControllerImpl;
import com.nucalale.app.controllers.AttendanceController;
import com.nucalale.app.dto.request.RequestAttendance;
import com.nucalale.app.dto.response.BaseResponse;
import com.nucalale.app.services.AttendanceService;
import com.nucalale.app.utils.ResponseHelper;
import lombok.RequiredArgsConstructor;

import java.time.LocalDate;

@BaseControllerImpl
@RequiredArgsConstructor
public class AttendanceControllerImpl implements AttendanceController {
    private final AttendanceService attendanceService;
    @Override
    public BaseResponse responseUserTimeTable(LocalDate date) {
        return ResponseHelper.createBaseResponse(attendanceService.getUserTimeTable(date));
    }

    @Override
    public BaseResponse getDetailTimeTable(String id) {
        return ResponseHelper.createBaseResponse(attendanceService.getDetailTimeTable(id));
    }

    @Override
    public BaseResponse attendance(RequestAttendance req) {
        return ResponseHelper.createBaseResponse(attendanceService.attendance(req));
    }
}
