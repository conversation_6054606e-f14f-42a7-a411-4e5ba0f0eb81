package com.nucalale.app.controllers.impl;

import com.nucalale.app.annotations.BaseControllerImpl;
import com.nucalale.app.controllers.AgencyController;
import com.nucalale.app.dto.request.RequestCreateAgency;
import com.nucalale.app.dto.request.RequestUpdateAgency;
import com.nucalale.app.dto.response.BaseResponse;
import com.nucalale.app.services.AgencyService;
import com.nucalale.app.utils.ResponseHelper;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@BaseControllerImpl
public class AgencyControllerImpl implements AgencyController {
    private final AgencyService agencyService;

    @Override
    public BaseResponse createAgency(RequestCreateAgency req) {
        return ResponseHelper.createBaseResponse(agencyService.createAgency(req));
    }

    @Override
    public BaseResponse getAgency() {
        return ResponseHelper.createBaseResponse(agencyService.listAgency());
    }

    @Override
    public BaseResponse getAgency(String id) {
        return ResponseHelper.createBaseResponse(agencyService.getAgency(id));
    }

    @Override
    public BaseResponse updateAgency(String id, RequestUpdateAgency req) {
        return ResponseHelper.createBaseResponse(agencyService.updateAgency(id, req));
    }
}
