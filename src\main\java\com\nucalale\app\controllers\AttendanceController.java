package com.nucalale.app.controllers;

import com.nucalale.app.annotations.BaseController;
import com.nucalale.app.dto.request.RequestAttendance;
import com.nucalale.app.dto.response.BaseResponse;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@BaseController("attendance")
public interface AttendanceController {

    @GetMapping("v1/user-time-table")
    BaseResponse responseUserTimeTable(@RequestParam(name = "date", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date);

    @GetMapping("v1/time-table/{id}")
    BaseResponse getDetailTimeTable(@PathVariable("id") String id);

    @PostMapping("v1")
    BaseResponse attendance(@RequestBody RequestAttendance req);
}
