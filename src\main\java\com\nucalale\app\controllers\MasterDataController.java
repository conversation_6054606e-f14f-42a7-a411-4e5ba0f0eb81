package com.nucalale.app.controllers;

import com.nucalale.app.annotations.BaseController;
import com.nucalale.app.dto.response.BaseResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@BaseController("master-data")
public interface MasterDataController {

    @GetMapping("v1/government-rank")
    BaseResponse getMasterDataGovernmentRank();

    @GetMapping("v1/religion")
    BaseResponse getListReligion();

    @GetMapping("v1/marital-status")
    BaseResponse getListMaritalStatus();

    @GetMapping("v1/check-nip")
    BaseResponse checkNik(@RequestParam(value = "nip") String nip);
}
