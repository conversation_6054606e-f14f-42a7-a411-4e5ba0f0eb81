package com.nucalale.app.services.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nucalale.app.dto.response.ResponseLabelValue;
import com.nucalale.app.entities.ReferenceOldDataEntity;
import com.nucalale.app.exceptions.BadRequestException;
import com.nucalale.app.exceptions.SystemErrorException;
import com.nucalale.app.repositories.ReferenceOldDataRepository;
import com.nucalale.app.services.MasterDataService;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Optional;

@Service
public class MasterDataServiceImpl implements MasterDataService {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final ReferenceOldDataRepository referenceOldDataRepository;

    public MasterDataServiceImpl(ReferenceOldDataRepository referenceOldDataRepository) {
        this.referenceOldDataRepository = referenceOldDataRepository;
    }

    @Override
    public List<ResponseLabelValue<String>> getMasterDataGovernmentRank() {
        try {
            return getDataAndMapping("data/government-rank.json");
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    @Override
    public List<ResponseLabelValue<String>> getListReligion() {
        return getDataAndMapping("data/religion-data.json");

    }


    @Override
    public List<ResponseLabelValue<String>> getListMaritalStatus() {
        return getDataAndMapping("data/marital-status.json");

    }

    @Override
    public ReferenceOldDataEntity checkNip(String nip) {
        Optional<ReferenceOldDataEntity> referenceOldDataEntity = referenceOldDataRepository.findByNip(nip);
        if (referenceOldDataEntity.isEmpty()) {
            return null;
        }
        try {
            return referenceOldDataEntity.get();
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }

    private List<ResponseLabelValue<String>> getDataAndMapping(String path) {
        try {
            ClassPathResource resource = new ClassPathResource(path);
            InputStream inputStream = resource.getInputStream();

            return objectMapper.readValue(inputStream, new TypeReference<List<ResponseLabelValue<String>>>() {
                @Override
                public Type getType() {
                    return super.getType();
                }
            });
        } catch (Exception e) {
            throw new SystemErrorException(e);
        }
    }
}
