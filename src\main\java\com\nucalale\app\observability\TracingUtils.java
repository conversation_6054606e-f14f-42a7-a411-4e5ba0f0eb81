package com.nucalale.app.observability;

import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;

/**
 * Utility class for custom tracing operations
 * 
 * This class provides convenient methods for creating custom spans
 * and adding observability to business operations.
 */
@Component
@Slf4j
public class TracingUtils {

    @Autowired
    private Tracer tracer;

    // Common attribute keys
    public static final AttributeKey<String> USER_ID = AttributeKey.stringKey("user.id");
    public static final AttributeKey<String> USER_TYPE = AttributeKey.stringKey("user.type");
    public static final AttributeKey<String> OPERATION_TYPE = AttributeKey.stringKey("operation.type");
    public static final AttributeKey<String> ENTITY_ID = AttributeKey.stringKey("entity.id");
    public static final AttributeKey<String> ENTITY_TYPE = AttributeKey.stringKey("entity.type");
    public static final AttributeKey<String> REQUEST_ID = AttributeKey.stringKey("request.id");
    public static final AttributeKey<Long> RECORD_COUNT = AttributeKey.longKey("record.count");
    public static final AttributeKey<String> ERROR_TYPE = AttributeKey.stringKey("error.type");
    public static final AttributeKey<String> ERROR_MESSAGE = AttributeKey.stringKey("error.message");

    /**
     * Execute a function within a custom span
     * 
     * @param spanName Name of the span
     * @param attributes Span attributes
     * @param operation The operation to execute
     * @return Result of the operation
     */
    public <T> T executeWithSpan(String spanName, Attributes attributes, Supplier<T> operation) {
        Span span = tracer.spanBuilder(spanName)
                .setAllAttributes(attributes)
                .startSpan();
        
        try (Scope scope = span.makeCurrent()) {
            T result = operation.get();
            span.setStatus(StatusCode.OK);
            return result;
        } catch (Exception e) {
            span.setStatus(StatusCode.ERROR, e.getMessage());
            span.recordException(e);
            throw e;
        } finally {
            span.end();
        }
    }

    /**
     * Execute a runnable within a custom span
     * 
     * @param spanName Name of the span
     * @param attributes Span attributes
     * @param operation The operation to execute
     */
    public void executeWithSpan(String spanName, Attributes attributes, Runnable operation) {
        Span span = tracer.spanBuilder(spanName)
                .setAllAttributes(attributes)
                .startSpan();
        
        try (Scope scope = span.makeCurrent()) {
            operation.run();
            span.setStatus(StatusCode.OK);
        } catch (Exception e) {
            span.setStatus(StatusCode.ERROR, e.getMessage());
            span.recordException(e);
            throw e;
        } finally {
            span.end();
        }
    }

    /**
     * Create a span for authentication operations
     */
    public Span createAuthenticationSpan(String operation, String userId, String userType) {
        return tracer.spanBuilder("auth." + operation)
                .setAttribute(USER_ID, userId)
                .setAttribute(USER_TYPE, userType)
                .setAttribute(OPERATION_TYPE, "authentication")
                .startSpan();
    }

    /**
     * Create a span for employee operations
     */
    public Span createEmployeeOperationSpan(String operation, String employeeId) {
        return tracer.spanBuilder("employee." + operation)
                .setAttribute(ENTITY_ID, employeeId)
                .setAttribute(ENTITY_TYPE, "employee")
                .setAttribute(OPERATION_TYPE, operation)
                .startSpan();
    }

    /**
     * Create a span for schedule operations
     */
    public Span createScheduleOperationSpan(String operation, String scheduleId) {
        return tracer.spanBuilder("schedule." + operation)
                .setAttribute(ENTITY_ID, scheduleId)
                .setAttribute(ENTITY_TYPE, "schedule")
                .setAttribute(OPERATION_TYPE, operation)
                .startSpan();
    }

    /**
     * Create a span for work unit operations
     */
    public Span createWorkUnitOperationSpan(String operation, String workUnitId, String agencyId) {
        return tracer.spanBuilder("workunit." + operation)
                .setAttribute(ENTITY_ID, workUnitId)
                .setAttribute(ENTITY_TYPE, "work_unit")
                .setAttribute("agency.id", agencyId)
                .setAttribute(OPERATION_TYPE, operation)
                .startSpan();
    }

    /**
     * Create a span for database operations
     */
    public Span createDatabaseOperationSpan(String operation, String table, String query) {
        return tracer.spanBuilder("db." + operation)
                .setAttribute("db.operation", operation)
                .setAttribute("db.table", table)
                .setAttribute("db.statement", query)
                .setAttribute("db.system", "postgresql")
                .startSpan();
    }

    /**
     * Create a span for external API calls
     */
    public Span createExternalApiSpan(String serviceName, String endpoint, String method) {
        return tracer.spanBuilder("external." + serviceName)
                .setAttribute("http.method", method)
                .setAttribute("http.url", endpoint)
                .setAttribute("service.name", serviceName)
                .setAttribute(OPERATION_TYPE, "external_api")
                .startSpan();
    }

    /**
     * Create a span for face recognition operations
     */
    public Span createFaceRecognitionSpan(String operation, String userId) {
        return tracer.spanBuilder("face_recognition." + operation)
                .setAttribute(USER_ID, userId)
                .setAttribute(OPERATION_TYPE, "face_recognition")
                .setAttribute("service.name", "face-recognition-service")
                .startSpan();
    }

    /**
     * Create a span for OTP operations
     */
    public Span createOtpOperationSpan(String operation, String userId, String otpType) {
        return tracer.spanBuilder("otp." + operation)
                .setAttribute(USER_ID, userId)
                .setAttribute("otp.type", otpType)
                .setAttribute(OPERATION_TYPE, "otp")
                .startSpan();
    }

    /**
     * Add common attributes to current span
     */
    public void addAttributesToCurrentSpan(String userId, String userType, String operation) {
        Span currentSpan = Span.current();
        if (currentSpan != null) {
            currentSpan.setAllAttributes(Attributes.of(
                    USER_ID, userId,
                    USER_TYPE, userType,
                    OPERATION_TYPE, operation
            ));
        }
    }

    /**
     * Record an error in the current span
     */
    public void recordError(Exception exception, String errorType) {
        Span currentSpan = Span.current();
        if (currentSpan != null) {
            currentSpan.setStatus(StatusCode.ERROR, exception.getMessage());
            currentSpan.recordException(exception);
            currentSpan.setAllAttributes(Attributes.of(
                    ERROR_TYPE, errorType,
                    ERROR_MESSAGE, exception.getMessage()
            ));
        }
    }

    /**
     * Record success in the current span
     */
    public void recordSuccess(String message) {
        Span currentSpan = Span.current();
        if (currentSpan != null) {
            currentSpan.setStatus(StatusCode.OK, message);
        }
    }

    /**
     * Add a custom event to the current span
     */
    public void addEvent(String eventName, Attributes attributes) {
        Span currentSpan = Span.current();
        if (currentSpan != null) {
            currentSpan.addEvent(eventName, attributes);
        }
    }
}
