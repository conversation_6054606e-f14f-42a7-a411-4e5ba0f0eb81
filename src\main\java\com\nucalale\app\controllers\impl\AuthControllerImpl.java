package com.nucalale.app.controllers.impl;

import com.nucalale.app.annotations.BaseControllerImpl;
import com.nucalale.app.controllers.AuthController;
import com.nucalale.app.dto.request.RequestEmployeeRegister;
import com.nucalale.app.dto.request.RequestRegisterOtherData;
import com.nucalale.app.dto.request.RequestSignIn;
import com.nucalale.app.dto.request.RequestSignInUser;
import com.nucalale.app.dto.response.BaseResponse;
import com.nucalale.app.services.AuthService;
import com.nucalale.app.utils.ResponseHelper;
import lombok.RequiredArgsConstructor;

@BaseControllerImpl
@RequiredArgsConstructor
public class AuthControllerImpl implements AuthController {
    private final AuthService authService;

    @Override
    public BaseResponse signInAdmin(RequestSignIn requestSignIn) {
        return ResponseHelper.createBaseResponse(authService.signInAdmin(requestSignIn));
    }

    @Override
    public BaseResponse requestOtpSingInUser(RequestSignInUser requestSignIn) {
        return ResponseHelper.createBaseResponse(authService.requestOtpSingInUser(requestSignIn));
    }

    @Override
    public BaseResponse signInUserWithOtp(RequestSignInUser requestSignIn) {

        return ResponseHelper.createBaseResponse(authService.signInUserWithOtp(requestSignIn));
    }

    @Override
    public BaseResponse signUpUser(RequestEmployeeRegister req) {
        return ResponseHelper.createBaseResponse(authService.signUpUser(req));
    }

    @Override
    public BaseResponse signUpWithOtpUser(RequestEmployeeRegister req) {
        return ResponseHelper.createBaseResponse(authService.signUpOtpUser(req));
    }

    @Override
    public BaseResponse addUserData(RequestEmployeeRegister req) {
        return ResponseHelper.createBaseResponse(authService.addUserData(req));
    }

    @Override
    public BaseResponse addWorkUnitData(RequestEmployeeRegister req) {
        return ResponseHelper.createBaseResponse(authService.addWorkUnitData(req));
    }

    @Override
    public BaseResponse addAddressData(RequestEmployeeRegister req) {
        return ResponseHelper.createBaseResponse(authService.addAddressData(req));
    }

    @Override
    public BaseResponse addOtherData(RequestRegisterOtherData req) {
        return ResponseHelper.createBaseResponse(authService.addOtherData(req));
    }


}
