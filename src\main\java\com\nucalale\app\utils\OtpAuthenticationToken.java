package com.nucalale.app.utils;

import org.springframework.security.authentication.AbstractAuthenticationToken;

public class OtpAuthenticationToken extends AbstractAuthenticationToken {
    private final String accountId;
    private final String otp;

    public OtpAuthenticationToken(String accountId, String otp) {
        super(null);
        this.accountId = accountId;
        this.otp = otp;
        setAuthenticated(false);
    }

    @Override
    public Object getCredentials() {
        return otp;
    }

    @Override
    public Object getPrincipal() {
        return accountId;
    }
}
